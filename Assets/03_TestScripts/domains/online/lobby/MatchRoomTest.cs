using System;
using System.Linq;
using System.Threading.Tasks;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandarts.domains.online.lobby;
using com.luxza.grandarts.domains.online.room;
using com.luxza.grandarts.domains.online.room.notification;
using com.luxza.grandarts.domains.online.unit;
using com.luxza.grandarts.domains.online.videochat;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.domains.stats;
using Moq;
using NUnit.Framework;
using R3;

namespace com.luxza.grandarts.tests.domains.online.lobby
{
    [TestFixture]
    public class MatchRoomTest
    {
        private OnlinePlayUnit _owner;
        private MedleyRequestFormat _playFormat;

        private MatchRoomId _matchRoomId;
        private LobbyId _lobbyId;

        [SetUp]
        public void SetUp()
        {
            _owner = CreateMockUnit(1);
            _playFormat = MedleyRequestFormat.Default(BoardSize.Inch155);
            _matchRoomId = new MatchRoomId("room1");
            _lobbyId = LobbyId.OpenLobbyId;
        }

        [Test]
        public void Join_ShouldAddParticipant_WhenRoomIsNotFull()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant = CreateMockUnit(2);

            matchRoom.Join(participant, new[] {
                ("host1", 10),
                ("host2", 20)
            });

            Assert.AreEqual(2, matchRoom.Participants.Count());
            Assert.Contains(participant, matchRoom.Participants.ToList());
        }

        [Test]
        public void Join_ShouldThrowMatchRoomIsFull_WhenRoomIsFull()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant1 = CreateMockUnit(2);
            var participant2 = CreateMockUnit(3);

            matchRoom.Join(participant1, new[] {
                ("host1", 10),
                ("host2", 20)
            });

            Assert.Throws<MatchRoomIsFull>(() => matchRoom.Join(participant2, new[] {
                ("host1", 10),
                ("host2", 20)
            }));
        }

        [Test]
        public void Join_ShouldThrowAlreadyExist_WhenParticipantAlreadyInRoom()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant = CreateMockUnit(2);

            matchRoom.Join(participant, new[] {
                ("host1", 10),
                ("host2", 20)
            });


            Assert.Throws<AlreadyExist>(() => matchRoom.Join(participant, new[] {
                ("host1", 10),
                ("host2", 20)
            }));
        }

        [Test]
        public void Leave_ShouldRemoveParticipant_WhenParticipantIsInRoom()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant = CreateMockUnit(2);

            matchRoom.Join(participant, new[] {
                ("host1", 10),
                ("host2", 20)
            });

            matchRoom.Leave(participant);

            Assert.AreEqual(1, matchRoom.Participants.Count());
            Assert.IsFalse(matchRoom.Participants.Contains(participant));
        }

        [Test]
        public void Leave_ShouldThrowAlreadyOutOfRoom_WhenParticipantIsNotInRoom()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant = CreateMockUnit(2);

            Assert.Throws<AlreadyOutOfRoom>(() => matchRoom.Leave(participant));
        }

        [Test]
        public void StartMatch_ShouldSetStatusToInProgress()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);

            matchRoom.StartMatch("PlayLog", new OnlineMatchServerInfo(), new VideoChatRoom());

            Assert.AreEqual(MatchRoomStatus.InProgress, matchRoom.Status);
        }

        [Test]
        public void FinishMatch_ShouldSetStatusToFinished()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);

            matchRoom.FinishMatch();

            Assert.AreEqual(MatchRoomStatus.Finished, matchRoom.Status);
        }

        [Test]
        public void Join_ShouldHandleConcurrentJoins()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId, 3);
            var participant1 = CreateMockUnit(2);
            var participant2 = CreateMockUnit(3);

            Parallel.Invoke(
                () => matchRoom.Join(participant1, new[] {
                    ("host1", 10),
                    ("host2", 20)
                }),
                () => matchRoom.Join(participant2, new[] {
                    ("host1", 10),
                    ("host2", 20)
                })
            );

            Assert.AreEqual(3, matchRoom.Participants.Count());
            Assert.Contains(_owner, matchRoom.Participants.ToList());
            Assert.Contains(participant1, matchRoom.Participants.ToList());
            Assert.Contains(participant2, matchRoom.Participants.ToList());
        }

        [Test]
        public void Leave_ShouldHandleConcurrentLeaves()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId, 3);
            var participant1 = CreateMockUnit(2);
            var participant2 = CreateMockUnit(3);

            matchRoom.Join(participant1, new[] {
                ("host1", 10),
                ("host2", 20)
            });
            matchRoom.Join(participant2, new[] {
                ("host1", 10),
                ("host2", 20)
            });

            Parallel.Invoke(
            () => matchRoom.Leave(participant1),
            () => matchRoom.Leave(participant2)
            );

            Assert.AreEqual(1, matchRoom.Participants.Count());
            Assert.IsFalse(matchRoom.Participants.Contains(participant1));
            Assert.IsFalse(matchRoom.Participants.Contains(participant2));
        }

        [Test]
        public void JoinAndLeave_ShouldHandleConcurrentJoinAndLeave()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId, 3);
            var participant1 = CreateMockUnit(2);
            var participant2 = CreateMockUnit(3);

            matchRoom.Join(participant1, new[] {
                ("host1", 10),
                ("host2", 20)
            });

            Parallel.Invoke(
                () => matchRoom.Join(participant1, new[] {
                    ("host1", 10),
                    ("host2", 20)
                }),
                () => matchRoom.Leave(participant1)
            );

            Assert.AreEqual(2, matchRoom.Participants.Count());
            Assert.IsTrue(matchRoom.Participants.Contains(participant2) || matchRoom.Participants.Contains(participant1));
        }

        [Test]
        public void Join_ShouldThrowAlreadyExist_WhenConcurrentJoinsSameParticipant()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId, 3);
            var participant = CreateMockUnit(2);

            Parallel.Invoke(
                () => matchRoom.Join(participant, new[] {
                    ("host1", 10),
                    ("host2", 20)
                }),
                () => Assert.Throws<AlreadyExist>(() => matchRoom.Join(participant, new[] {
                    ("host1", 10),
                    ("host2", 20)
                }))
            );

            Assert.AreEqual(2, matchRoom.Participants.Count());
            Assert.Contains(participant, matchRoom.Participants.ToList());
        }

        [Test]
        public void Join_ShouldThrowMatchRoomIsFull_WhenConcurrentJoinsExceedLimit()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant1 = CreateMockUnit(2);
            var participant2 = CreateMockUnit(3);

            Parallel.Invoke(
                () => matchRoom.Join(participant1, new[] {
                    ("host1", 10),
                    ("host2", 20)
                }),
                () => Assert.Throws<MatchRoomIsFull>(() => matchRoom.Join(participant2, new[] {
                    ("host1", 10),
                    ("host2", 20)
                }))
            );

            Assert.AreEqual(2, matchRoom.Participants.Count());
        }

        [Test]
        public void Leave_ShouldThrowAlreadyOutOfRoom_WhenConcurrentLeavesSameParticipant()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant = CreateMockUnit(2);

            matchRoom.Join(participant, new[] {
                ("host1", 10),
                ("host2", 20)
            });

            Parallel.Invoke(
                () => matchRoom.Leave(participant),
                () => Assert.Throws<AlreadyOutOfRoom>(() => matchRoom.Leave(participant))
            );

            Assert.AreEqual(1, matchRoom.Participants.Count());
            Assert.IsFalse(matchRoom.Participants.Contains(participant));
        }

        [Test]
        public void ParticipantsChanged_ShouldTrigger_WhenParticipantJoins()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant = CreateMockUnit(2);
            bool eventTriggered = false;

            matchRoom.ParticipantsChanged.Subscribe(_ => eventTriggered = true);

            matchRoom.Join(participant, new[] {
                ("host1", 10),
                ("host2", 20)
            });

            Assert.IsTrue(eventTriggered);
        }

        [Test]
        public void ParticipantsChanged_ShouldTrigger_WhenParticipantLeaves()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant = CreateMockUnit(2);
            bool eventTriggered = false;

            matchRoom.ParticipantsChanged.Subscribe(_ => eventTriggered = true);

            matchRoom.Join(participant, new[] {
                ("host1", 10),
                ("host2", 20)
            });
            matchRoom.Leave(participant);

            Assert.IsTrue(eventTriggered);
        }

        [Test]
        public void Status_ShouldChangeToPreparing_WhenRoomIsFull()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant = CreateMockUnit(2);

            matchRoom.Join(participant, new[] {
                ("host1", 10),
                ("host2", 20)
            });

            Assert.AreEqual(MatchRoomStatus.Preparing, matchRoom.Status);
        }

        [Test]
        public void Status_ShouldChangeToWaitingForOpponent_WhenParticipantLeaves()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant = CreateMockUnit(2);

            matchRoom.Join(participant, new[] {
                ("host1", 10),
                ("host2", 20)
            });
            matchRoom.Leave(participant);

            Assert.AreEqual(MatchRoomStatus.WaitingForOpponent, matchRoom.Status);
        }

        [Test]
        public void OnRoomUpdated_ShouldCalled_WhenRoomWasUpdated()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant = CreateMockUnit(2);
            bool eventTriggered = false;

            matchRoom.OnRoomUpdated.Subscribe(_ => eventTriggered = true);

            matchRoom.Join(participant, new[] {
                ("host1", 10),
                ("host2", 20)
            });
            var factoryMock = new Mock<IRoomNotificationCeterFactory>();
            var mockNotificationCenter = new Mock<IRoomNotificationCenter>();
            var subject = new Subject<MatchRoom>();
            mockNotificationCenter.SetupGet(n => n.OnUpdated).Returns(subject);
            factoryMock.Setup(f =>
                f.Create(It.IsAny<MatchRoom>())
            )
            .Returns(mockNotificationCenter.Object);
            matchRoom.StartSubscribe(factoryMock.Object);

            subject.OnNext(matchRoom);
            Assert.IsTrue(eventTriggered);
        }

        [Test]
        public void OnRoomUpdated_ShouldNotCalled_WhenStartSubscribeNotCalled()
        {
            var matchRoom = new MatchRoom(_matchRoomId, _lobbyId, _owner, _playFormat, _owner.HostPlayerId);
            var participant = CreateMockUnit(2);
            bool eventTriggered = false;

            matchRoom.OnRoomUpdated.Subscribe(_ => eventTriggered = true);

            matchRoom.Join(participant, new[] {
                ("host1", 10),
                ("host2", 20)
            });
            var factoryMock = new Mock<IRoomNotificationCeterFactory>();
            var mockNotificationCenter = new Mock<IRoomNotificationCenter>();
            var subject = new Subject<MatchRoom>();
            mockNotificationCenter.SetupGet(n => n.OnUpdated).Returns(subject);
            factoryMock.Setup(f =>
                f.Create(It.IsAny<MatchRoom>())
            )
            .Returns(mockNotificationCenter.Object);

            subject.OnNext(matchRoom);
            Assert.IsFalse(eventTriggered);
        }

        private OnlinePlayUnit CreateMockUnit(int id)
        {
            var playerId = new PlayerId(id);
            var granId = new GranId($"Gran{id}");
            var mockPlayer = new Mock<IPlayer>();
            mockPlayer.SetupGet(p => p.Id).Returns(playerId);
            mockPlayer.SetupGet(p => p.GranId).Returns(granId);
            mockPlayer.Setup(p => p.ZeroOne80Stats).Returns(Stats.CreateZeroOne80StatsByRating(10));
            mockPlayer.Setup(p => p.ZeroOne100Stats).Returns(Stats.CreateZeroOne100StatsByRating(10));
            mockPlayer.Setup(p => p.CR80Stats).Returns(Stats.CreateCR80StatsByRating(10));
            mockPlayer.Setup(p => p.CR100Stats).Returns(Stats.CreateCR100StatsByRating(10));

            return new OnlinePlayUnit(new PlayUnitMember[] { new PlayUnitMember(mockPlayer.Object, DeviceId.MyDeviceId) });
        }
    }
}