using System.Threading;
using System.Threading.Tasks;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.online.lobby;
using com.luxza.grandarts.domains.online.unit;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.domains.stats;
using com.luxza.grandarts.usecases.online.lobby;
using com.luxza.grandarts.usecases.player;
using Cysharp.Threading.Tasks;
using Moq;
using NUnit.Framework;

namespace com.luxza.grandarts.tests.usecases.online.lobby
{
    public class AcceptJoinRoomRequestUsecaseTest
    {
        private Mock<ILobbyService> _mockLobbyServiceAPIClient;
        private Mock<IPlayerPresenceRepository> _mockPlayerPresenceRepository;
        private AcceptJoinRoomRequestUsecase _usecase;

        [SetUp]
        public void SetUp()
        {
            _mockLobbyServiceAPIClient = new Mock<ILobbyService>();
            _mockPlayerPresenceRepository = new Mock<IPlayerPresenceRepository>();
            _usecase = new AcceptJoinRoomRequestUsecase(_mockLobbyServiceAPIClient.Object, _mockPlayerPresenceRepository.Object);
        }

        [Test]
        public async Task ExecuteAsync_ShouldReturnMatchRoom_WhenRequestIsAccepted()
        {
            // Arrange
            var mockPlayer = new Mock<IPlayer>();
            mockPlayer.Setup(player => player.Id).Returns(new PlayerId(1));
            mockPlayer.Setup(p => p.ZeroOne80Stats).Returns(Stats.CreateZeroOne80StatsByRating(10));
            mockPlayer.Setup(p => p.ZeroOne100Stats).Returns(Stats.CreateZeroOne100StatsByRating(10));
            mockPlayer.Setup(p => p.CR80Stats).Returns(Stats.CreateCR80StatsByRating(10));
            mockPlayer.Setup(p => p.CR100Stats).Returns(Stats.CreateCR100StatsByRating(10));
            var unit = OnlinePlayUnitFactory.Create(mockPlayer.Object);
            var matchRoom = new MatchRoom(
                new MatchRoomId("1"),
                lobbyId: LobbyId.OpenLobbyId,
                unit,
                MedleyRequestFormat.Default(BoardSize.Inch155),
                unit.HostPlayerId);
            var cancellationToken = CancellationToken.None;
            _mockLobbyServiceAPIClient
                .Setup(client => client.AcceptJoinRequestAsync(unit, matchRoom, cancellationToken))
                .Returns(UniTask.FromResult(matchRoom));

            // Act
            var result = await _usecase.ExecuteAsync(unit, matchRoom, cancellationToken);

            // Assert
            Assert.AreEqual(matchRoom, result);
            _mockLobbyServiceAPIClient.Verify(client => client.AcceptJoinRequestAsync(unit, matchRoom, cancellationToken), Times.Once);
        }

        [Test]
        public void ExecuteAsync_ShouldThrowException_WhenRequestFails()
        {
            // Arrange
            var mockPlayer = new Mock<IPlayer>();
            mockPlayer.Setup(player => player.Id).Returns(new PlayerId(1));
            mockPlayer.Setup(p => p.ZeroOne80Stats).Returns(Stats.CreateZeroOne80StatsByRating(10));
            mockPlayer.Setup(p => p.ZeroOne100Stats).Returns(Stats.CreateZeroOne100StatsByRating(10));
            mockPlayer.Setup(p => p.CR80Stats).Returns(Stats.CreateCR80StatsByRating(10));
            mockPlayer.Setup(p => p.CR100Stats).Returns(Stats.CreateCR100StatsByRating(10));
            var unit = OnlinePlayUnitFactory.Create(mockPlayer.Object);
            var matchRoom = new MatchRoom(
                new MatchRoomId("1"),
                lobbyId: LobbyId.OpenLobbyId,
                unit,
                MedleyRequestFormat.Default(BoardSize.Inch155),
                unit.HostPlayerId);
            var cancellationToken = CancellationToken.None;
            _mockLobbyServiceAPIClient
                .Setup(client => client.AcceptJoinRequestAsync(unit, matchRoom, cancellationToken))
                .Throws(new System.Exception("Request failed"));

            // Act & Assert
            Assert.ThrowsAsync<System.Exception>(async () => await _usecase.ExecuteAsync(unit, matchRoom, cancellationToken));
            _mockLobbyServiceAPIClient.Verify(client => client.AcceptJoinRequestAsync(unit, matchRoom, cancellationToken), Times.Once);
        }
    }
}