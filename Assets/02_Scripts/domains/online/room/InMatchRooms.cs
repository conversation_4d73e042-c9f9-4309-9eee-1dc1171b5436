using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using com.luxza.grandarts.utils.dataStructure;
using com.luza.grandarts.domains.online.lobby.notification;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.domains.online.lobby
{
    public class InMatchRooms
    {
        public readonly LobbyId Id;
        private PageableList<MatchRoom> _inMatchRooms;

        private Func<CancellationToken, UniTask<PageableList<MatchRoom>>> Loader;

        public InMatchRooms(LobbyId lobbyId,Func<CancellationToken, UniTask<PageableList<MatchRoom>>> loader)
        {
            if (loader == null)
            {
                throw new ArgumentNullException($"{nameof(loader)} must not be null");
            }
            Id = lobbyId;
            Loader = loader;
        }

        public async UniTask<PageableList<MatchRoom>> LoadFirstPageAsync(CancellationToken cancellationToken)
        {
            var list = await Loader.Invoke(cancellationToken);
            _inMatchRooms = list;
            return _inMatchRooms;
        }

        public UniTask LoadNextPageAsync(CancellationToken cancellationToken)
        {
            if (_inMatchRooms == null)
            {
                throw new InvalidOperationException("You need to load first page before load next page.");
            }
            if (!_inMatchRooms.HasNextPage)
            {
                throw new InvalidOperationException("There is no next page.");
            }
            return _inMatchRooms.LoadNextPage(cancellationToken);
        }

        public UniTask LoadPreviousPageAsync(CancellationToken cancellationToken)
        {
            if (_inMatchRooms == null)
            {
                throw new InvalidOperationException("You need to load first page before load previous page.");
            }
            if (!_inMatchRooms.HasPreviousPage)
            {
                throw new InvalidOperationException("There is no previous page.");
            }
            return _inMatchRooms.LoadPreviousPage(withReload: false, cancellationToken: cancellationToken);
        }

        public UniTask ReloadAll(CancellationToken cancellationToken)
        {
            if (_inMatchRooms == null)
            {
                throw new InvalidOperationException("You need to load first page before reload current page.");
            }
            return _inMatchRooms.ReloadAll(cancellationToken);
        }

        public IEnumerable<MatchRoom> Rooms => _inMatchRooms.Pages.SelectMany(p => p.Items);
    }
}