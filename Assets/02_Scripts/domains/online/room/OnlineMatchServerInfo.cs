using com.luxza.grandarts.domains.game.setting;

namespace com.luxza.grandarts.domains.online.room
{
    //TODO: Refactor this to increase the level of abstraction.
    public class OnlineMatchServerInfo
    {
        public string Host;
        public string RoomId;

        //This is used for a match.
        //The order of the first leg is decided by the online match server.
        //So, we can't decide the order until the match creation.
        //We have order at the first leg in setting, but it is just for setting like random.
        //We should not change the setting when the match has started.
        public ThrowOrderAtFirstLeg ThrowOrderAtFirstLeg;
    }
}