using System;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandarts.domains.player
{
    public class PlayerSetting
    {
        public bool IncludeSeparateBullAndFatBullToStats { get; private set; } = false;
        public TargetStatsRate TargetStatsRate { get; private set; } = TargetStatsRate._80;
        public bool IsShowStats80InDefault => TargetStatsRate == TargetStatsRate._80;
        public bool IsShowStats100InDefault => TargetStatsRate == TargetStatsRate._100;
        public bool EnableAwardMovie { get; private set; } = true;
        public bool EnableHitEffect { get; private set; } = true;
        public bool EnableCaller { get; private set; } = true;
        public float HitEffectOpacity { get; private set; } = 1f;
        public bool AutoRoundChange { get; private set; } = false;
        public bool EnableProMode { get; set; } = true;

        public RoundInputType RoundInputType { get; private set; } = RoundInputType.Segment;

        public InputDevice InputDevice
        {
            get;
            set;
        } = InputDevice.NoDevice;

        public readonly PlayerId PlayerId;

        private PlayerSetting(PlayerId playerId)
        {
            PlayerId = playerId;
        }

        public PlayerSetting
        (
            PlayerId playerId,
            TargetStatsRate defaultStatsPercentage = TargetStatsRate._80,
            bool enableAwardMovie = true,
            bool enableHitEffect = true,
            bool enableCaller = true,
            bool enableProMode = true,
            float hitEffectOpacity = 1f,
            bool autoRoundChange = false,
            bool analyzeSepaBullAndFatBull = false,
            RoundInputType roundInputType = RoundInputType.Segment,
            InputDevice inputDevice = InputDevice.NoDevice
        )
        {
            PlayerId = playerId;
            TargetStatsRate = defaultStatsPercentage;
            EnableAwardMovie = enableAwardMovie;
            EnableHitEffect = enableHitEffect;
            EnableCaller = enableCaller;
            EnableProMode = enableProMode;
            HitEffectOpacity = hitEffectOpacity;
            AutoRoundChange = autoRoundChange;
            IncludeSeparateBullAndFatBullToStats = analyzeSepaBullAndFatBull;
            InputDevice = inputDevice;
            RoundInputType = roundInputType;
        }
        public static PlayerSetting Default(PlayerId playerId, BoardSize boardSize)
        {
            switch (boardSize)
            {
                case BoardSize.NonSelected: return new PlayerSetting(playerId);
                case BoardSize.Inch155: return DefaultFor155Player(playerId);
                case BoardSize.Inch132: return DefaultFor132Player(playerId);
                case BoardSize.Steel: return DefaultForSteelPlayer(playerId);
                default: throw new ArgumentOutOfRangeException(nameof(boardSize), boardSize, null);
            }
        }

        public static PlayerSetting DefaultFor155Player(PlayerId playerId)
        {
            return new PlayerSetting(playerId)
            {
                TargetStatsRate = TargetStatsRate._80,
                RoundInputType = RoundInputType.Segment,
                InputDevice = InputDevice.GranBoard
            };
        }

        public static PlayerSetting DefaultFor132Player(PlayerId playerId)
        {
            return new PlayerSetting(playerId)
            {
                TargetStatsRate = TargetStatsRate._100,
                RoundInputType = RoundInputType.Segment,
                InputDevice = InputDevice.GranBoard
            };
        }

        public static PlayerSetting DefaultForSteelPlayer(PlayerId playerId)
        {
            return new PlayerSetting(playerId)
            {
                TargetStatsRate = TargetStatsRate._100,
                RoundInputType = playerId.IsCPU() ? RoundInputType.Segment : RoundInputType.Visit
            };
        }

        public PlayerSetting DeepCopy()
        {
            return new PlayerSetting(PlayerId)
            {
                IncludeSeparateBullAndFatBullToStats = this.IncludeSeparateBullAndFatBullToStats,
                TargetStatsRate = this.TargetStatsRate,
                EnableAwardMovie = this.EnableAwardMovie,
                EnableHitEffect = this.EnableHitEffect,
                EnableCaller = this.EnableCaller,
                EnableProMode = this.EnableProMode,
                HitEffectOpacity = this.HitEffectOpacity,
                AutoRoundChange = this.AutoRoundChange,
                RoundInputType = this.RoundInputType
            };
        }

        public PlayerSetting SwitchDefaultStatsPercentage()
        {
            if (IsShowStats80InDefault)
            {
                return UpdateDefaultStatsPercentageTo100();
            }
            else
            {
                return UpdateDefaultStatsPercentageTo80();
            }
        }

        public PlayerSetting UpdateDefaultStatsPercentageTo80()
        {
            TargetStatsRate = TargetStatsRate._80;
            return this;
        }

        public PlayerSetting UpdateDefaultStatsPercentageTo100()
        {
            TargetStatsRate = TargetStatsRate._100;
            return this;
        }

        public static PlayerSetting CPUSetting(PlayerId playerId, BoardSize boardSize)
        {
            var setting = Default(playerId, boardSize);
            setting.EnableProMode = false;
            setting.InputDevice = InputDevice.NoDevice;
            setting.RoundInputType = RoundInputType.Segment;
            return setting;
        }
    }
}