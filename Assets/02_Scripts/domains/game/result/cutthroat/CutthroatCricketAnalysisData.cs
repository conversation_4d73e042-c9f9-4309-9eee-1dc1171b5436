using System.Collections.Generic;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.cricket;

namespace com.luxza.grandarts.domains.game.result.cutthroat {
    public struct CutthroatCricketAnalysisData
    {
        public readonly float Stats80;
        public readonly float Stats100;
        public readonly float ShootRate;
        public readonly float KeepRate;
        public readonly float BedRate;
        public readonly int Whitehorse;
        public readonly float WhitehorseRate;
        public readonly int FiveMarks;
        public readonly int SixMarks;
        public readonly int SevenMarks;
        public readonly int EightMarks;
        public readonly int NineMarks;
        public readonly float FiveMarksRate;
        public readonly float SixMarksRate;
        public readonly float SevenMarksRate;
        public readonly float EightMarksRate;
        public readonly float NineMarksRate;
        public readonly float TripleRate;

        /// <summary>
        /// Whether it was possible to open in first round.
        /// </summary>
        public readonly bool IsFirstRoundOpen;

        /// <summary>
        /// Gain marks in the first round.
        /// </summary>
        public readonly int FirstRoundMarkCount;

        /// <summary>
        /// Whether the player won without being outscored by the opponent.
        //  It will be false if outscored even once.
        //  It will be false if all players have no score and the game ends in a draw.
        /// </summary>
        public readonly bool IsHavingControl;

        /// <summary>
        /// Whether the player won after being outscored by the opponent by 100 points or more.
        /// </summary>
        public readonly bool IsComeback;

        /// <summary>
        /// Number of darts required to get 3 marks in 20~Bull area.
        /// Minimum 0, Maximum 3.
        /// </summary>
        public readonly int[] DartCountToGet3Marks;

        /// <summary>
        /// Whether the player has opened 20~BULL areas.
        /// </summary>
        public readonly bool[] HasOpenedAreas;

        /// <summary>
        /// Whether the player has closed the 20~BULL areas that the opponent unit has opened before being scored in the later rounds.
        /// The points scored in the round where the opponent opened are not included in the additional points.
        /// </summary>
        public readonly bool[] HasClosedAreaBeforeOpponentScored;

        public readonly CricketRoundSummary[] RoundSummaries;

        public readonly Dictionary <Award, int> AwardCounts;

        public readonly int SBullCount;
        public readonly int DBullCount;
        public readonly int T20Count;

        public CutthroatCricketAnalysisData(
            float stats80,
            float stats100,
            float shoot,
            float keep,
            int whitehorse,
            float bedrate,
            float whitehorseRate,
            int fiveMarks,
            int sixMarks,
            int sevenMarks,
            int eightMarks,
            int nineMarks,
            float fiveMarksRate,
            float sixMarksRate,
            float sevenMarksRate,
            float eightMarksRate,
            float nineMarksRate,
            float tripleRate,
            bool[] hasOpenedAreas,
            bool[] hasClosedAreaBeforeOpponentScored,
            int[] dartCountToGet3Marks,
            bool isFirstRoundOpen,
            int firstRoundMarkCount,
            bool isHavingControl,
            bool isComeback,
            CricketRoundSummary[] roundSummaries,
            Dictionary<Award, int> awardCounts,
            int sBullCount,
            int dBullCount,
            int t20Count
        )
        {
            Stats80 = stats80;
            Stats100 = stats100;
            ShootRate = shoot;
            KeepRate = keep;
            Whitehorse = whitehorse;
            BedRate = bedrate;
            WhitehorseRate = whitehorseRate;
            FiveMarks = fiveMarks;
            SixMarks = sixMarks;
            SevenMarks = sevenMarks;
            EightMarks = eightMarks;
            NineMarks = nineMarks;
            FiveMarksRate = fiveMarksRate;
            SixMarksRate = sixMarksRate;
            SevenMarksRate = sevenMarksRate;
            EightMarksRate = eightMarksRate;
            NineMarksRate = nineMarksRate;
            TripleRate = tripleRate;

            HasOpenedAreas = hasOpenedAreas;
            HasClosedAreaBeforeOpponentScored = hasClosedAreaBeforeOpponentScored;

            DartCountToGet3Marks = dartCountToGet3Marks;
            IsFirstRoundOpen = isFirstRoundOpen;
            FirstRoundMarkCount = firstRoundMarkCount;
            IsHavingControl = isHavingControl;
            IsComeback = isComeback;
            RoundSummaries = roundSummaries;
            AwardCounts = awardCounts;

            SBullCount = sBullCount;
            DBullCount = dBullCount;
            T20Count = t20Count;
        }
    }
}