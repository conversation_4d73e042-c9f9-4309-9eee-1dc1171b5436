using System.Linq;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandarts.domains.player;
using com.luxza.grandartslogic.domain;
using com.luxza.grandartslogic.domain.game.countup;

namespace com.luxza.grandarts.domains.game.factory
{
    public static class RefereeCountUpFactory {
        public static (RefereeCountUp referee, PlayUnit[] playUnits) Create(
            CountUpPlayFormat playFormat,
            TargetStatsRate targetStatsRate,
            bool allPlayerUsingGranEye = false
        ) {
            var playUnits = PlayUnitFactory.CreateUnits(playFormat.EntrySlots);
            var dartsLogicUnits = playUnits.Select(p => DartsLogicUnitFactory.Create(p, playFormat.RequestFormat.GameCode, targetStatsRate, allPlayerUsingGranEye));
            return (MatchMaker.MakeCountUp(
                dartsLogicUnits.ToArray(),
                GameRuleFactory.Create(playFormat.RequestFormat)), playUnits.ToArray());
        }
    }
}