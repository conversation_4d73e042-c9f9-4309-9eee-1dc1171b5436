using com.luxza.grandartslogic.domain.game;
using UnityEngine;

namespace com.luxza.grandarts.domains.game.input {
    public static class SegmentCodeExtensions
    {
        public static Color ToDisplayColor(this SegmentCode key)
        {
            if (key.IsBull())
            {
                return GetHexColor(key.IsDouble() ? Color.red : Color.green);
            }
            else if (key.IsDouble())
            {
                return GetHexColor(Color.green);
            }
            else if (key.IsTriple())
            {
                return GetHexColor(Color.red);
            }
            return GetHexColor(Color.white);

            Color GetHexColor(Color color)
            {
                if (color == Color.red)
                {
                    if (ColorUtility.TryParseHtmlString("#c31515", out Color result)) return result;
                    else return color;
                }
                else if (color == Color.green)
                {
                    if (ColorUtility.TryParseHtmlString("#4bab18", out Color result)) return result;
                    else return color;
                }
                else if (color == Color.white)
                {
                    if (ColorUtility.TryParseHtmlString("#FFFFFF", out Color result)) return result;
                    else return color;
                }
                else
                {
                    if (ColorUtility.TryParseHtmlString("#464646", out Color result)) return result;
                    else return color;
                }
            }
        }
    }
 }