namespace com.luxza.grandarts.domains.game.unit
{
    public class UnitId
    {
        public readonly int Value;
        public readonly string StringValue;

        public UnitId(int value)
        {
            Value = value;
            StringValue = value.ToString();
        }

        public override bool Equals(object obj)
        {
            if (obj is UnitId)
            {
                return Value == ((UnitId)obj).Value;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return Value;
        }

        public override string ToString()
        {
            return StringValue;
        }

        public static bool operator ==(UnitId a, UnitId b)
        {
            return a.Value == b.Value;
        }

        public static bool operator !=(UnitId a, UnitId b)
        {
            return a.Value != b.Value;
        }

        public static UnitId Empty => new UnitId(0);

        public static implicit operator int(UnitId unitId)
        {
            return unitId.Value;
        }

        public static explicit operator UnitId(int value)
        {
            return new UnitId(value);
        }
    }
}