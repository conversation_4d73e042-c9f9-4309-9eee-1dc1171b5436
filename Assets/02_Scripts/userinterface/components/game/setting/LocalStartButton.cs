using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.infrastructures.graneye;
using com.luxza.grandarts.userinterfaces.components.localize;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.ui.components.molecules;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace com.luxza.grandarts.userinterfaces.components.game.setting
{
    public class LocalStartButton : MonoBehaviour
    {
        [SerializeField] protected SelectPlayerView _selectPlayerView;
        [SerializeField]
		protected GranButton _button;

        private void Awake()
        {
            _button.AddOnClickActionWithLoading(OnClick, destroyCancellationToken);
        }

        protected virtual bool EnableGameStart()
        {
            if (Application.isEditor)
            {
                return _selectPlayerView.IsAnyPlayerSelected;
            }
            else
            {
                return _selectPlayerView.IsAnyPlayerSelected && !_selectPlayerView.IsEnteredCPUOnly;
            }
        }

        private void Update()
        {
            _button.interactable = EnableGameStart();
        }

        protected virtual UniTask OnClick()
        {
            return UniTask.CompletedTask;
        }

        protected bool CheckGranEyeForcedUpdate()
        {
            if (GranEyeSettingSaver.IsGranEye)
            {
                if (GranEye.IsHaveCameraForceVersion() || GranEye.IsHaveEchoForceVersion())
                {
                    MessageUtility.ShowMessageModal(LocalizeString.GetLocalizedString(LocalizeKey.Notifications.GranEyePlayGameForcedUpdate), GranModal.IconType.OK,
                        (LocalizeString.GetLocalizedString(LocalizeKey.Labels.GranEyeUpdate),async () =>
                        {
                            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.GranEyeFirmwareUpdate));
                        }),
                        (LocalizeString.GetLocalizedString(LocalizeKey.Labels.GranEyeUpdateLater),async () =>
                        {

                        }));
                    return true;
                }
                return false;
            }
            return false;
        }
    }
}