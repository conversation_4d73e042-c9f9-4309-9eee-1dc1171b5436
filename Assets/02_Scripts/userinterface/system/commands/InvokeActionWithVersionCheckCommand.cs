using System;
using System.Threading;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.usecases.system;
using com.luxza.grandarts.userinterfaces.components.localize;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace com.luxza.grandarts.userinterface.system.commands
{
    public class InvokeActionWithVersionCheckCommand
    {
#if UNITY_ANDROID
        private const string AppStoreLink = "https://play.google.com/store/apps/details?id=jp.luxza.GranDarts";
#elif UNITY_IOS
        private const string AppStoreLink = "https://apps.apple.com/us/app/grandarts/id6743786735";
#endif
        public async UniTask ExecuteAsync(
            Func<UniTask> onActionLatestVersion,
            CancellationToken cancellationToken
        )
        {
            var status = await new GetCurrentVersionStatusUseCase(DIContainer.Instance.Resolve<IAppVersionRepository>()).ExecuteAsync(cancellationToken);
            if (status == domains.appversion.AppVersion.VersionStatus.Latest)
            {
                if (onActionLatestVersion != null)
                {
                    await onActionLatestVersion.Invoke();
                }
            }
            else if (status == domains.appversion.AppVersion.VersionStatus.Current)
            {
                string updateText = string.Format(LocalizeString.GetLocalizedString(LocalizeKey.Notifications.AppUpdateNotification), Application.version);
                MessageUtility.ShowMessageModal(
                    updateText,
                    GranModal.IconType.Error,
                    (LocalizeString.GetLocalizedString(LocalizeKey.Labels.Update), positiveAction),
                    (LocalizeString.GetLocalizedString(LocalizeKey.Labels.NoUpdateLogin), negativeAction)
                );

                UniTask positiveAction()
                {
                    Application.OpenURL(AppStoreLink);
                    Application.Quit();
                    return UniTask.CompletedTask;
                }

                UniTask negativeAction()
                {
                    onActionLatestVersion?.Invoke();
                    return UniTask.CompletedTask;
                }

            }
            else if (status == domains.appversion.AppVersion.VersionStatus.Outdated)
            {
                string updateText = string.Format(LocalizeString.GetLocalizedString(LocalizeKey.Notifications.AppUpdateNotification), Application.version);
                MessageUtility.ShowMessageModal(
                    updateText,
                    GranModal.IconType.Error,
                    (LocalizeString.GetLocalizedString(LocalizeKey.Labels.Update), positiveAction),
                    (LocalizeString.GetLocalizedString(LocalizeKey.Labels.AppQuit), negativeAction)
                );

                UniTask positiveAction()
                {
                    Application.OpenURL(AppStoreLink);
                    Application.Quit();
                    return UniTask.CompletedTask;
                }

                UniTask negativeAction()
                {
                    Application.Quit();
                    return UniTask.CompletedTask;
                }
            }
        }
    }
}