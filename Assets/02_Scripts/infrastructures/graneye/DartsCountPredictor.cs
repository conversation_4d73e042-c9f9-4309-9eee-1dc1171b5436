using System;
using System.Linq;
using System.Threading;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.utils;
using com.luxza.graneye;
using com.luxza.granlog;
using Cysharp.Threading.Tasks;
using R3;

namespace com.luxza.grandarts.infrastructures.graneye
{
    public class DartsCountPredictor  : IDisposable
    {
        private static TimeSpan INTERVAL_FOR_CHECK = TimeSpan.FromSeconds(2);
        private CancellationTokenSource _cancellationTokenSource = new();

        Subject<int> _onDartsCountPredicted = new();

        public Observable<int> OnDartsCountPredicted => _onDartsCountPredicted;

        public void Run()
        {
            if (!GranEyeSettingSaver.IsGranEye) return;
            if (GranEye.GranEyeCore == null) throw new ArgumentException("GranEyeCore is null");

            UniTask.RunOnThreadPool(async () =>
            {
                // 首次启动前等待初始间隔
                await UniTask.Delay(INTERVAL_FOR_CHECK, cancellationToken: _cancellationTokenSource.Token);

                while (true)
                {
                    try
                    {
                        Log.d("Attempting to predict dart count.");

                        if (GranEyeSettingSaver.IsAutoChangeDartModel)
                        {
                            var dartCount = await GranEye.GranEyeCore.DartPredictForAutoChange(
                            GranEyeCore.CameraPos.Left,
                            _cancellationTokenSource.Token);

                            _cancellationTokenSource.Token.ThrowIfCancellationRequested();
                            var count = dartCount;

                            Log.d($"Predicted dart count: {count}");
                            _onDartsCountPredicted.OnNext(count);
                            if (count == 0)
                            {
                                Log.i($"Dart count is {count}, stopping prediction loop.");
                                break;
                            }
                        }
                        else
                        {
                            var dartCount = await GranEye.GranEyeCore.DartCountPredict(
                            GranEyeCore.CameraPos.Left,
                            _cancellationTokenSource.Token);

                            _cancellationTokenSource.Token.ThrowIfCancellationRequested();
                            if (dartCount == null)
                            {
                                Log.w("GranEye.GranEyeCore.DartCountPredict returned null.");
                                // 预测失败后等待间隔再继续
                                await UniTask.Delay(INTERVAL_FOR_CHECK, cancellationToken: _cancellationTokenSource.Token);
                                continue;

                            }
                            var count = dartCount.Any() ? dartCount.Max(t => t.Tag) : 0;
                            Log.d($"Predicted dart count: {count}");
                            _onDartsCountPredicted.OnNext(count);
                            if (count <= 1)
                            {
                                Log.i($"Dart count is {count}, stopping prediction loop.");
                                break;
                            }
                        }

                        // 预测成功后等待间隔再进行下一次预测
                        await UniTask.Delay(INTERVAL_FOR_CHECK, cancellationToken: _cancellationTokenSource.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        Log.i("DartsCountPredictor run was canceled.");
                        break;
                    }
                    catch (Exception ex)
                    {
                        Log.e($"Error in dart detection loop: {ex.Message}\n{ex.StackTrace}");
                        _onDartsCountPredicted.OnNext(-1);
                        // 发生异常后等待间隔再继续
                        await UniTask.Delay(INTERVAL_FOR_CHECK, cancellationToken: _cancellationTokenSource.Token);
                        continue;
                    }
                }
                Log.i("DartsCountPredictor task ended.");
            }, cancellationToken: _cancellationTokenSource.Token).SafeForget(e =>
            {
                // OperationCanceledException是预期的取消行为，不应作为错误记录
                if (e is OperationCanceledException)
                    Log.d($"DartsCountPredictor任务被取消: {e.Message}");
                else
                    Log.e($"DartsCountPredictor UniTask failed: {e}");
            });
        }

        public void Stop()
        {
            _cancellationTokenSource.Cancel();
            _onDartsCountPredicted.Dispose();
        }

        public void Dispose()
        {
            Stop();
        }
    }
}