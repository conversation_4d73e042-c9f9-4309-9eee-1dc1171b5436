using System;
using System.Collections.Generic;
using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.game.setting;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.infrastructures.mock.online.setting
{

    public class MockOnlineRequestFormatRepository : IOnlineRequestFormatRepository
    {
        Dictionary<PlayerId, OnlineRequestFormat> _requestFormats = new Dictionary<PlayerId, OnlineRequestFormat>();

        public UniTask<OnlineRequestFormat> GetAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            if(_requestFormats.ContainsKey(playerId))
            {
                return UniTask.FromResult(_requestFormats[playerId]);
            }
            throw new NotFound($"RequestFormat for {playerId} is not found");
        }

        public async UniTask<OnlineRequestFormat> SaveAsync(PlayerId playerId, OnlineRequestFormat requestFormat, CancellationToken cancellationToken)
        {
            await UniTask.Delay(TimeSpan.FromSeconds(1.0f), cancellationToken: cancellationToken);
            if (_requestFormats.ContainsKey(playerId))
            {
                _requestFormats[playerId] = requestFormat;
            }
            else
            {
                _requestFormats.Add(playerId, requestFormat);
            }

            return requestFormat;
        }
    }
}