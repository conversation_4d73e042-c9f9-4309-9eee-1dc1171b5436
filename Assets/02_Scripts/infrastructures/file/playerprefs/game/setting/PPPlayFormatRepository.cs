using System;
using System.Linq;
using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.game.setting;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.KickDown;
using com.luxza.grandartslogic.domain.game.multiplecr;
using com.luxza.grandartslogic.domain.game.rotation;
using com.luxza.grandartslogic.domain.game.target20;
using com.luxza.grandartslogic.domain.game.zeroone;
using com.luxza.granlog;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace com.luxza.grandarts.infrastructures.file.game.setting {
    public class PPLocalGamePlayFormatRepository : ILocalGameRequestFormatRepository
    {
        private const string KEY_X01 = "PLAY_FORMAT_X01";
        private const string KEY_CRICKET = "PLAY_FORMAT_CRICKET";
        private const string KEY_KICKDOWN = "PLAY_FORMAT_KICKDOWN";
        private const string KEY_X01_MEDLEY = "PLAY_FORMAT_X01_MEDLEY";
        private const string KEY_CRICKET_MEDLEY = "PLAY_FORMAT_CRICKET_MEDLEY";
        private const string KEY_MIX_MEDLEY = "PLAY_FORMAT_MIX_MEDLEY";
        private const string KEY_TARGET20 = "PLAY_FORMAT_TARGET20";
        private const string KEY_ROTATION = "PLAY_FORMAT_ROTATION";
        private const string KEY_BEYONDTOP = "PLAY_FORMAT_BEYONDTOP";
        private const string KEY_MULTIPLECR = "PLAY_FORMAT_MULTIPLECR";
        private const string KEY_SHANGHAI = "PLAY_FORMAT_SHANGHAI";

        /// <summary>
        /// {0} = GameCode, {1} = inCondition, {2} = outCondition, {3} = MaxRound, {4} = HandicapSetting
        /// </summary>
        private const string VALUE_X01_FORMAT = "{0}:{1}:{2}:{3}:{4}";

        /// <summary>
        /// {0} = GameCode, {1} = MaxRound, {2} = HandicapSetting
        /// </summary>
        private const string VALUE_CR_FORMAT = "{0}:{1}:{2}";
        /// <summary>
        /// {0} = GameCode, {1} = inCondition, {2} = outCondition, {3} = OverScoreSetting, {4} = MaxRound
        /// </summary>
        private const string VALUE_KICKDOWN_FORMAT = "{0}:{1}:{2}:{3}:{4}";

        /// <summary>
        /// {0} = Set number, {1} = Leg number , {2}= Bestof or FirstTo, {3} = GameCodes separated by = , {4} = inCondition, {5} = outCondition, {6} = MaxRound, {7} = HandicapSetting
        /// </summary>
        private const string VALUE_MEDLEY_FORMAT = "{0}:{1}:{2}:{3}:{4}:{5}:{6}:{7}";
        /// <summary>
        /// {0} = TargetClearCondition, {1} = GameMode
        /// </summary>
        private const string VALUE_TARGET20_FORMAT = "{0}:{1}";
        private const string VALUE_ROTATION_FORMAT = "{0}:{1}:{2}:{3}";
        private const string VALUE_BEYONDTOP_FORMAT = "{0}";
        private const string VALUE_MULTIPLECR_FORMAT = "{0}";
        private const string VALUE_SHANGHAI_FORMAT = "{0}:{1}";

        UniTask<x01RequestFormat> ILocalGameRequestFormatRepository.GetX01RequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            var settingStr = PlayerPrefs.GetString(KEY_X01, null);
            if(string.IsNullOrEmpty(settingStr)) {
                throw new NotFound();
            }

            var settings = settingStr.Split(':');
            var gameCode = Enum.Parse<GameCode>(settings[0]);
            var inCondition = Enum.Parse<InCondition>(settings[1]);
            var outCondition = Enum.Parse<OutCondition>(settings[2]);
            var maxRound = int.Parse(settings[3]);
            var handicapSetting = bool.Parse(settings[4]);

            var format = new x01RequestFormat()
            {
                GameCode = gameCode,
                InCondition = inCondition,
                OutCondition = outCondition,
                MaxRound = new MaxRound(maxRound),
                HandicapSetting = handicapSetting ? grandartslogic.domain.settings.HandicapSetting.Auto : grandartslogic.domain.settings.HandicapSetting.None
            };

            return UniTask.FromResult(format);
        }

        public UniTask<x01RequestFormat> SaveX01RequestFormatAsync(PlayerId playerId, x01RequestFormat requestFormat, CancellationToken cancellationToken)
        {
            var gameCode = Enum.GetName(typeof(GameCode), requestFormat.GameCode);
            var inCondition = Enum.GetName(typeof(InCondition), requestFormat.InCondition);
            var outCondition = Enum.GetName(typeof(OutCondition), requestFormat.OutCondition);
            var maxRound = requestFormat.MaxRound.Value;
            var handicapSetting = requestFormat.HandicapSetting == grandartslogic.domain.settings.HandicapSetting.Auto;

            var value = string.Format(VALUE_X01_FORMAT, gameCode, inCondition, outCondition, maxRound, handicapSetting);
            PlayerPrefs.SetString(KEY_X01, value);
            return UniTask.FromResult(requestFormat);
        }

        public UniTask<CricketRequestFormat> GetCricketRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            var settingStr = PlayerPrefs.GetString(KEY_CRICKET, null);
            if(string.IsNullOrEmpty(settingStr)) {
                throw new NotFound();
            }

            var settings = settingStr.Split(':');
            var gameCode = Enum.Parse<GameCode>(settings[0]);
            var maxRound = int.Parse(settings[1]);
            var handicapSetting = bool.Parse(settings[2]);

            var format = new CricketRequestFormat()
            {
                GameCode = gameCode,
                MaxRound = new MaxRound(maxRound),
                HandicapSetting = handicapSetting ? grandartslogic.domain.settings.HandicapSetting.Auto : grandartslogic.domain.settings.HandicapSetting.None
            };

            return UniTask.FromResult(format);
        }

        public UniTask<CricketRequestFormat> SaveCricketRequestFormatAsync(PlayerId playerId, CricketRequestFormat requestFormat, CancellationToken cancellationToken)
        {
            var gameCode = Enum.GetName(typeof(GameCode), requestFormat.GameCode);
            var maxRound = requestFormat.MaxRound.Value;
            var handicapSetting = requestFormat.HandicapSetting == grandartslogic.domain.settings.HandicapSetting.Auto;

            var value = string.Format(VALUE_CR_FORMAT, gameCode, maxRound, handicapSetting);
            PlayerPrefs.SetString(KEY_CRICKET, value);
            return UniTask.FromResult(requestFormat);
        }

        private UniTask<MedleyRequestFormat> GetMedleyRequestFormatAsync(PlayerId playerId, string key, CancellationToken cancellationToken) {
            var settingStr = PlayerPrefs.GetString(key, null);
            if(string.IsNullOrEmpty(settingStr)) {
                throw new NotFound();
            }

            Log.d($"Get Medley Setting: {settingStr}");

            var settings = settingStr.Split(':');
            var sets = new Sets(int.Parse(settings[0]));
            var legs = new Legs(int.Parse(settings[1]), Enum.Parse<Legs.Format>(settings[2]));
            var gameCodes = settings[3].Split('=').Select(c =>
            {
                if(Enum.TryParse<GameCode>(c, out var gameCode)) {
                    return (GameCode?)gameCode;
                } else {
                    return null;
                }
            }).ToArray();
            var inCondition = Enum.Parse<InCondition>(settings[4]);
            var outCondition = Enum.Parse<OutCondition>(settings[5]);
            var maxRound = int.Parse(settings[6]);
            var handicapSetting = bool.Parse(settings[7]);

            var format = new MedleyRequestFormat()
            {
                InCondition = inCondition,
                OutCondition = outCondition,
                MaxRound = new MaxRound(maxRound),
                HandicapSetting = handicapSetting ? grandartslogic.domain.settings.HandicapSetting.Auto : grandartslogic.domain.settings.HandicapSetting.None
            };

            format.SetSets(sets, legs, gameCodes);

            return UniTask.FromResult(format);
        }

        public UniTask<MedleyRequestFormat> GetX01MedleyRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            return GetMedleyRequestFormatAsync(playerId, KEY_X01_MEDLEY, cancellationToken);
        }

        public UniTask<MedleyRequestFormat> GetCricketMedleyRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            return GetMedleyRequestFormatAsync(playerId, KEY_CRICKET_MEDLEY, cancellationToken);
        }

        public UniTask<MedleyRequestFormat> GetMixMedleyRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            return GetMedleyRequestFormatAsync(playerId, KEY_MIX_MEDLEY, cancellationToken);
        }

        public UniTask<MedleyRequestFormat> SaveX01MedleyRequestFormatAsync(PlayerId playerId, MedleyRequestFormat requestFormat, CancellationToken cancellationToken)
        {
            var gameCodes = string.Join("=", requestFormat.GameCodes.Select(c => Enum.GetName(typeof(GameCode), c)));
            var inCondition = Enum.GetName(typeof(InCondition), requestFormat.InCondition);
            var outCondition = Enum.GetName(typeof(OutCondition), requestFormat.OutCondition);
            var maxRound = requestFormat.MaxRound.Value;
            var handicapSetting = requestFormat.HandicapSetting == grandartslogic.domain.settings.HandicapSetting.Auto;
            var sets = requestFormat.Sets.Value;
            var legs = requestFormat.Legs.Value;
            var bestOfOrFirstTo = Enum.GetName(typeof(Legs.Format), requestFormat.Legs.LegsFormat);

            var value = string.Format(VALUE_MEDLEY_FORMAT, sets, legs, bestOfOrFirstTo, gameCodes, inCondition, outCondition, maxRound, handicapSetting);
            Log.d($"Save X01 Medley Setting: {value}");
            PlayerPrefs.SetString(KEY_X01_MEDLEY, value);
            return UniTask.FromResult(requestFormat);
        }

        public UniTask<MedleyRequestFormat> SaveCricketMedleyRequestFormatAsync(PlayerId playerId, MedleyRequestFormat requestFormat, CancellationToken cancellationToken)
        {
            var gameCodes = string.Join("=", requestFormat.GameCodes.Select(c => Enum.GetName(typeof(GameCode), c)));
            var inCondition = Enum.GetName(typeof(InCondition), requestFormat.InCondition);
            var outCondition = Enum.GetName(typeof(OutCondition), requestFormat.OutCondition);
            var maxRound = requestFormat.MaxRound.Value;
            var handicapSetting = requestFormat.HandicapSetting == grandartslogic.domain.settings.HandicapSetting.Auto;
            var sets = requestFormat.Sets.Value;
            var legs = requestFormat.Legs.Value;
            var bestOfOrFirstTo = Enum.GetName(typeof(Legs.Format), requestFormat.Legs.LegsFormat);

            var value = string.Format(VALUE_MEDLEY_FORMAT, sets, legs, bestOfOrFirstTo, gameCodes, inCondition, outCondition, maxRound, handicapSetting);
            PlayerPrefs.SetString(KEY_CRICKET_MEDLEY, value);
            Log.d($"Save Cricket Medley Setting: {value}");
            return UniTask.FromResult(requestFormat);
        }

        public UniTask<MedleyRequestFormat> SaveMixMedleyRequestFormatAsync(PlayerId playerId, MedleyRequestFormat requestFormat, CancellationToken cancellationToken)
        {
            var gameCodes = string.Join("=", requestFormat.GameCodes.Select(c => {
                if (c == null) return "no-choice";
                return Enum.GetName(typeof(GameCode), c);
            }));
            var inCondition = Enum.GetName(typeof(InCondition), requestFormat.InCondition);
            var outCondition = Enum.GetName(typeof(OutCondition), requestFormat.OutCondition);
            var maxRound = requestFormat.MaxRound.Value;
            var handicapSetting = requestFormat.HandicapSetting == grandartslogic.domain.settings.HandicapSetting.Auto;
            var sets = requestFormat.Sets.Value;
            var legs = requestFormat.Legs.Value;
            var bestOfOrFirstTo = Enum.GetName(typeof(Legs.Format), requestFormat.Legs.LegsFormat);

            var value = string.Format(VALUE_MEDLEY_FORMAT, sets, legs, bestOfOrFirstTo, gameCodes, inCondition, outCondition, maxRound, handicapSetting);
            PlayerPrefs.SetString(KEY_MIX_MEDLEY, value);
            Log.d($"Save Mix Medley Setting: {value}");
            return UniTask.FromResult(requestFormat);
        }

        public UniTask<KickDownRequestFormat> GetKickDownRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            var settingStr = PlayerPrefs.GetString(KEY_KICKDOWN, null);
            if(string.IsNullOrEmpty(settingStr)) {
                throw new NotFound();
            }

            var settings = settingStr.Split(':');
            var gameCode = Enum.Parse<GameCode>(settings[0]);
            var inCondition = Enum.Parse<InCondition>(settings[1]);
            var outCondition = Enum.Parse<OutCondition>(settings[2]);
            var overScoreSetting = Enum.Parse<KickDownOverScoreSetting>(settings[3]);
            var maxRound = int.Parse(settings[4]);

            var format = new KickDownRequestFormat()
            {
                GameCode = gameCode,
                InCondition = inCondition,
                OutCondition = outCondition,
                OverScoreSetting = overScoreSetting,
                MaxRound = new MaxRound(maxRound)
            };

            return UniTask.FromResult(format);
        }

        public UniTask<KickDownRequestFormat> SaveKickDownRequestFormatAsync(PlayerId playerId, KickDownRequestFormat requestFormat, CancellationToken cancellationToken)
        {
            var gameCode = Enum.GetName(typeof(GameCode), requestFormat.GameCode);
            var inCondition = Enum.GetName(typeof(InCondition), requestFormat.InCondition);
            var outCondition = Enum.GetName(typeof(OutCondition), requestFormat.OutCondition);
            var overScoreSetting = Enum.GetName(typeof(KickDownOverScoreSetting), requestFormat.OverScoreSetting);
            var maxRound = requestFormat.MaxRound.Value;

            var value = string.Format(VALUE_KICKDOWN_FORMAT, gameCode, inCondition, outCondition, overScoreSetting, maxRound);
            PlayerPrefs.SetString(KEY_KICKDOWN, value);
            return UniTask.FromResult(requestFormat);
        }

        #region Target 20

        public UniTask<Target20RequestFormat> GetTarget20RequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            var settingStr = PlayerPrefs.GetString(KEY_TARGET20, null);
            if(string.IsNullOrEmpty(settingStr)) {
                throw new NotFound();
            }

            var settings = settingStr.Split(':');
            var targetClearCondition = Enum.Parse<TargetClearCondition>(settings[0]);
            var maxRound = MaxRound.Free();
            switch (targetClearCondition)
            {
                case TargetClearCondition.Round10:
                    maxRound = 10;
                    break;
            }
            var gameMode = Enum.Parse<GameRuleTarget20.Target20GameMode>(settings[1]);

            var format = new Target20RequestFormat()
            {
                TargetClearCondition = targetClearCondition,
                GameMode = gameMode,
                MaxRound = new MaxRound(maxRound)
            };

            return UniTask.FromResult(format);
        }

        public UniTask<Target20RequestFormat> SaveTarget20RequestFormatAsync(PlayerId playerId, Target20RequestFormat requestFormat,
            CancellationToken cancellationToken)
        {
            var targetClearCondition = Enum.GetName(typeof(TargetClearCondition), requestFormat.TargetClearCondition);
            var gameMode = Enum.GetName(typeof(GameRuleTarget20.Target20GameMode), requestFormat.GameMode);
            var value = string.Format(VALUE_TARGET20_FORMAT, targetClearCondition, gameMode);
            PlayerPrefs.SetString(KEY_TARGET20, value);
            return UniTask.FromResult(requestFormat);
        }
        
        #endregion

        public UniTask<RotationRequestFormat> GetRotationRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            var settingStr = PlayerPrefs.GetString(KEY_ROTATION, null);
            if(string.IsNullOrEmpty(settingStr)) {
                throw new NotFound();
            }
            var settings = settingStr.Split(':');
            var directionOption = Enum.Parse<RotationDirectionOption>(settings[0]);
            var targetOption = Enum.Parse<RotationTargetOption>(settings[1]);
            var noJump = false;
            if (settings.Length > 2 && !string.IsNullOrEmpty(settings[2]))
            {
                noJump = bool.Parse(settings[2]);
            }
            var continues = false;
            if (settings.Length > 3 && !string.IsNullOrEmpty(settings[3]))
            {
                continues = bool.Parse(settings[3]);
            }
            var format = new RotationRequestFormat()
            {
                RotationOption = new RotationOption(directionOption, targetOption, noJump, continues)
            };
            return UniTask.FromResult(format);
        }

        public UniTask<RotationRequestFormat> SaveRotationRequestFormatAsync(PlayerId playerId, RotationRequestFormat requestFormat,
            CancellationToken cancellationToken)
        {
            var targetClearCondition = Enum.GetName(typeof(RotationDirectionOption), requestFormat.RotationOption.DirectionOption);
            var gameMode = Enum.GetName(typeof(RotationTargetOption), requestFormat.RotationOption.TargetOption);
            var value = string.Format(VALUE_ROTATION_FORMAT, targetClearCondition, gameMode,
                requestFormat.RotationOption.NoJump,
                requestFormat.RotationOption.Continues);
            PlayerPrefs.SetString(KEY_ROTATION, value);
            return UniTask.FromResult(requestFormat);
        }

        public UniTask<BeyondTopRequestFormat> GetBeyondTopRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            var settingStr = PlayerPrefs.GetString(KEY_BEYONDTOP, null);
            if(string.IsNullOrEmpty(settingStr)) {
                throw new NotFound();
            }
            var settings = settingStr.Split(':');
            var maxRound = int.Parse(settings[0]);
            var format = new BeyondTopRequestFormat()
            {
                MaxRound = new MaxRound(maxRound)
            };
            return UniTask.FromResult(format);
        }

        public UniTask<BeyondTopRequestFormat> SaveBeyondTopRequestFormatAsync(PlayerId playerId, BeyondTopRequestFormat requestFormat,
            CancellationToken cancellationToken)
        {
            var value = string.Format(VALUE_BEYONDTOP_FORMAT, requestFormat.MaxRound.Value);
            PlayerPrefs.SetString(KEY_BEYONDTOP, value);
            return UniTask.FromResult(requestFormat);
        }

        public UniTask<ShangHaiRequestFormat> GetShangHaiRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            var settingStr = PlayerPrefs.GetString(KEY_SHANGHAI, null);
            if(string.IsNullOrEmpty(settingStr)) {
                throw new NotFound();
            }
            var settings = settingStr.Split(':');
            var maxRound = int.Parse(settings[0]);
            var halfRoundList = settings[1].Split('|').Select(s => int.TryParse(s, out int num) ? num : 0).ToArray();
            var format = new ShangHaiRequestFormat()
            {
                MaxRound = new MaxRound(maxRound),
                HalfRoundOption = halfRoundList
            };
            return UniTask.FromResult(format);
        }

        public UniTask<ShangHaiRequestFormat> SaveShangHaiRequestFormatAsync(PlayerId playerId, ShangHaiRequestFormat requestFormat,
            CancellationToken cancellationToken)
        {
            var value = string.Format(VALUE_SHANGHAI_FORMAT, requestFormat.MaxRound.Value,
                requestFormat.HalfRoundToString());
            PlayerPrefs.SetString(KEY_SHANGHAI, value);
            return UniTask.FromResult(requestFormat);
        }

        public UniTask<MultipleCRRequestFormat> GetMultipleCRRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            var settingStr = PlayerPrefs.GetString(KEY_MULTIPLECR, null);
            if(string.IsNullOrEmpty(settingStr)) {
                throw new NotFound();
            }
            var settings = settingStr.Split(':');
            var option = Enum.Parse<GameRuleMultipleCR.MultipleCROption>(settings[0]);
            var format = new MultipleCRRequestFormat()
            {
                MaxRound = new MaxRound(8),
                Option = option
            };
            return UniTask.FromResult(format);
        }

        public UniTask<MultipleCRRequestFormat> SaveMultipleCRRequestFormatAsync(PlayerId playerId, MultipleCRRequestFormat requestFormat,
            CancellationToken cancellationToken)
        {
            var option = Enum.GetName(typeof(GameRuleMultipleCR.MultipleCROption), requestFormat.Option);
            var value = string.Format(VALUE_MULTIPLECR_FORMAT, option);
            PlayerPrefs.SetString(KEY_MULTIPLECR, value);
            return UniTask.FromResult(requestFormat);
        }

        public UniTask ClearAllSettingsAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            PlayerPrefs.DeleteKey(KEY_X01);
            PlayerPrefs.DeleteKey(KEY_CRICKET);
            PlayerPrefs.DeleteKey(KEY_KICKDOWN);
            PlayerPrefs.DeleteKey(KEY_X01_MEDLEY);
            PlayerPrefs.DeleteKey(KEY_CRICKET_MEDLEY);
            PlayerPrefs.DeleteKey(KEY_MIX_MEDLEY);
            PlayerPrefs.DeleteKey(KEY_TARGET20);
            PlayerPrefs.DeleteKey(KEY_ROTATION);
            PlayerPrefs.DeleteKey(KEY_BEYONDTOP);

            return UniTask.CompletedTask;
        }
    }
}