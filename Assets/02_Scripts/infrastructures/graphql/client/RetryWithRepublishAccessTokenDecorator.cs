using System.Linq;
using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.user;
using com.luxza.grandarts.infrastructures.graphql.cache;
using com.luxza.grandarts.infrastructures.graphql.requests.auth;
using com.luxza.granlog;
using com.luxza.unitygraphqlclient;
using com.luxza.unitygraphqlclient.exceptions;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.infrastructures.graphql.client {
    public class RetryWithRepublishAccessTokenDecorator : IGraphQLAPIDecorator
    {
        private readonly IGraphQLAPIDecorator _decorator;

        public RetryWithRepublishAccessTokenDecorator(IGraphQLAPIDecorator decorator)
        {
            _decorator = decorator;
        }

        public GraphQLAPIClient Client => _decorator.Client;

        public async UniTask<T> PostAsync<T>(API api, object args, AuthToken accessToken, CancellationToken cancellationToken) where T : IGraphQLResponse
        {
            try {
                return await _decorator.PostAsync<T>(api, args, accessToken, cancellationToken);
            } catch (GraphQLError e) {
                if (
                    e.Response.ErrorCodes.Contains("Unauthenticated") ||
                    e.Response.ErrorCodes.Contains("ID_TOKEN_AUTH_FAILED")
                )
                {
                    Log.w("Auth failed with invalid token. We will try after republish access token.");
                    if (AuthCacheService.TryGetAuths(out var auths))
                    {
                        var refreshToken = auths.refreshToken;
                        var newToken = await new RefreshTokenRequest(auths.refreshToken).SendAsync(cancellationToken);
                        AuthCacheService.SaveAuths(newToken, refreshToken);
                        return await PostAsync<T>(api, args, newToken, cancellationToken);
                    }
                    else
                    {
                        throw new SessionTimeOutException(e);
                    }
                }
                else if (
                    e.Response.ErrorCodes.Contains("COGNITO_USER_NOT_FOUND_EXCEPTION")
                )
                {
                    throw new SessionTimeOutException(e);
                }

                throw;
            }
        }
    }
}