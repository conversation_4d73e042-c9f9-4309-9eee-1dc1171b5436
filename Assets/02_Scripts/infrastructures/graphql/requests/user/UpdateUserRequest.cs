using System.Globalization;
using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.language;
using com.luxza.grandarts.domains.user;
using com.luxza.grandarts.infrastructures.graphql.cache;
using com.luxza.grandarts.infrastructures.graphql.client;
using com.luxza.grandarts.infrastructures.graphql.converter;
using com.luxza.grandarts.usecases.auth;
using com.luxza.unitygraphqlclient;
using com.luxza.unitygraphqlclient.gen;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.infrastructures.graphql.requests.user {
    internal class UpdateUserRequest : IGraphQLRequest<User>
    {
        private readonly object _parameter;

        public UpdateUserRequest(UserId userId, RegionInfo country, Language language) {
            _parameter = new
            {
                data = new
                {
                    user_id = userId.Value,
                    country_code = country.TwoLetterISORegionName,
                    language_code = language.Code(),
                }
            };
        }
        public async UniTask<User> SendAsync(CancellationToken cancellationToken)
        {
            IGraphQLAPIDecorator client = new GraphQLAPIClientBasicDecorator(new GraphQLAPIClient());
            client = new TimeOutDecorator(client);
            client = new GraphQLAPIClientRetryDecorator(client);
            client = new RetryWithRepublishAccessTokenDecorator(client);
            client = new RetryWithLoginDecorator(client);

            if(!AuthCacheService.TryGetAuths(out var auth)) {
                throw new SessionTimeOutException("Auth is null");
            }

            if(!ApplicationAuth.IsLoggedIn) {
                throw new SessionTimeOutException("Not logged in");
            }

            if(ApplicationAuth.LoggedInUser == null) {
                throw new SessionTimeOutException("User is null");
            }

            var res = await client.PostAsync<UpdateUserResponse>(
                new UpdateUserAPI(),
                _parameter,
                auth.accessToken,
                cancellationToken
            );

            ApplicationAuth.LoggedInUser.Language = res.update_user.language.ToModel();
            ApplicationAuth.LoggedInUser.Country = new RegionInfo(res.update_user.countryCode);

            return ApplicationAuth.LoggedInUser;
        }

        class UpdateUserResponse : IGraphQLResponse {
            public Types.User update_user;
        }

        class UpdateUserAPI : API
        {
            public override string Name => "UpdateUser";
        }
    }
}