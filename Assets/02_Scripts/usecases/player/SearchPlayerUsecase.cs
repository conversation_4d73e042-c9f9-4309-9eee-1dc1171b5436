using System.Threading;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.utils.dataStructure;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.usecases.player
{
    public class SearchPlayerUsecase
    {
        private readonly ISearchPlayerServiceAPIClient _searchPlayerServiceAPIClient;

        public SearchPlayerUsecase(ISearchPlayerServiceAPIClient searchPlayerServiceAPIClient)
        {
            _searchPlayerServiceAPIClient = searchPlayerServiceAPIClient;
        }

        /// <summary>
        /// Search players by filter.
        /// </summary>
        /// <param name="filter">filter</param>
        /// <param name="cancellationToken"></param>
        /// <returns>Players</returns>
        public async UniTask<PageableList<IPlayer>> SearchPlayersAsync(SearchPlayerFilter filter, CancellationToken cancellationToken)
        {
            return await _searchPlayerServiceAPIClient.SearchPlayersAsync(filter, cancellationToken);
        }
    }
}