using System.Collections.Generic;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.teamcr
{
    public partial class RefereeTeamCRTest
    {
        public static TestCaseData[] ScoreTestCases = new TestCaseData[]
        {
            new TestCaseData(
                    Teams_A_Doubles_Team_B_Doubles,
                    new Segment[]
                {
                    Segment.Triple20,
                    Segment.Triple20,
                    Segment.Triple20,
                }, "A", 0, 0)
                .SetName("1 TeamA Score At Round1 nad Throw1 is 0 when TeamA -> hit R1[T20, T20, T20]")
                .Returns(0),
            new TestCaseData(
                    Teams_A_Doubles_Team_B_Doubles,
                    new Segment[]
                {
                    Segment.Triple20,
                    Segment.Triple20,
                    Segment.Triple20,

                    Segment.Miss,
                    Segment.Miss,
                    Segment.Miss,

                    Segment.Triple20,
                    Segment.Triple20,
                    Segment.Triple20,
                }, "A", 1, 2)
                .SetName("2 TeamA Score At Round2 nad Throw2 is 120 when TeamA -> hit R1[T20, T20, T20]")
                .Returns(60),
            new TestCaseData(
                    Teams_A_Doubles_Team_B_Doubles,
                    new Segment[]
                {
                    Segment.OUT,
                    Segment.OUT,
                    Segment.OUT,
                }, "A", 0, 2)
                .SetName("3 All hit OUT -> hit R1[OUT, OUT, OUT]")
                .Returns(0),

            //クローズされた系
            new TestCaseData(
                    Teams_A_Doubles_Team_B_Doubles,
                    new Segment[]
                {
                    Segment.Triple20,
                    Segment.Miss,
                    Segment.Miss,

                    Segment.Triple20,
                    Segment.Double16,
                    Segment.Miss,

                    Segment.Double20,
                }, "A", 1, 0)
                .SetName("4 TeamA Score At Round2 nad Throw1 is 0 when TeamA -> hit R1[T20, T20, T20] R2[D20] TeamB -> R1[T20, D16, Miss]")
                .Returns(0),
        };


        [TestCaseSource(nameof(ScoreTestCases))]
        public int ScoreTest(List<Unit> teams, Segment[] hits, string teamId, int roundIndex, int throwIndex)
        {
            var referee = StartMatchAndToProceed(teams, hits);
            return referee.Scorer.Score(teamId, roundIndex, throwIndex);
        }
    }
}