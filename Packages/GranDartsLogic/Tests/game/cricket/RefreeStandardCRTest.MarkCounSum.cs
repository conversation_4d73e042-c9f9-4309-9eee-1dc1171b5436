using System.Collections.Generic;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.cricket;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.cricket
{
    public partial class RefereeStandardCRTest
    {
        public static TestCaseData[] MarkCountSumTestCases = new TestCaseData[]
        {
            new TestCaseData(
                    Team_A_Singles_TeamB_Singles,
                    new Segment[0],
                    "A",
                    CricketMarkPosition.MarkPosition20)
                .Returns(new int[5])
                .SetName("No hit, first team"),
            new TestCaseData(
                    Team_A_Singles_TeamB_Singles,
                    new Segment[]
                    {
                        Segment.Double20,
                        Segment.Double1,
                        Segment.Double19
                    },
                    "A",
                    CricketMarkPosition.MarkPosition20)
                .Returns(new int[5])
                .SetName("team a mark count 0"),
            new TestCaseData(
                    Team_A_Singles_TeamB_Singles,
                    new Segment[]
                    {
                        Segment.Triple20,
                        Segment.Triple20,
                        Segment.Triple20,
                    },
                    "A",
                    CricketMarkPosition.MarkPosition20)
                .Returns(new int[] {0,0,0,0,1})
                .SetName("team a 9mark 1"),
            new TestCaseData(
                    Team_A_Singles_TeamB_Singles,
                    new Segment[]
                    {
                        Segment.Double20,
                        Segment.Double15,
                        Segment.Double15,

                        Segment.Triple10,
                        Segment.Double5,
                        Segment.Double5,

                        Segment.Triple20,
                        Segment.Single18In,
                        Segment.Single18In,
                    },
                    "A",
                    CricketMarkPosition.MarkPosition20)
                .Returns(new int[] {1,1,0,0,0})
                .SetName("team a 5mark 1, 6mark 1"),
            new TestCaseData(
                    Team_A_Singles_TeamB_Singles,
                    new Segment[]
                    {
                        Segment.Double20,
                        Segment.Double15,
                        Segment.Double15,

                        Segment.Triple10,
                        Segment.Double11,
                        Segment.Double11,

                        Segment.Double20,
                        Segment.Double15,
                        Segment.Double15,
                    },
                    "A",
                    CricketMarkPosition.MarkPositionBull)
                .Returns(new int[] {0,2,0,0,0})
                .SetName("team a 6mark 2 "),
        };

        [TestCaseSource(nameof(MarkCountSumTestCases))]
        public int[] MarkCountSumTest(List<Unit> teams, Segment[] hits, string teamId, CricketMarkPosition position)
        {
            var referee = StartMatchAndToProceed(teams, hits);
            int[] awardcounts = new int[]{ referee.Scorer.GetTeamCountAwardSum(teamId, 5),
                                         referee.Scorer.GetTeamCountAwardSum(teamId, 6),
                                         referee.Scorer.GetTeamCountAwardSum(teamId, 7),
                                         referee.Scorer.GetTeamCountAwardSum(teamId, 8),
                                         referee.Scorer.GetTeamCountAwardSum(teamId, 9)};

            return awardcounts;
        }

    }
}