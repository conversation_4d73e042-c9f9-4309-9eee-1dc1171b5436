using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.Oniren;
using NUnit.Framework;
using static com.luxza.grandartslogic.domain.game.Oniren.GameRuleOniren;

namespace com.luxza.grandartslogic.tests.game.oniren
{
    public partial class OnirenRefereeTest
    {


        public static TestCaseData[] RoundScoreTestcases = new TestCaseData[]
        {
            //testcase その1 1人で8Rまでやった場合
            new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                    //R1TeamA
                    Segment.Single1Out,
                    Segment.Single1Out,
                    Segment.Single2In,//success 2

                    //R2TeamA
                    Segment.Double1,
                    Segment.Single1Out,
                    Segment.Single1Out,//success 2

                    //R3TeamA
                    Segment.Triple1,
                    Segment.Single1Out,
                    Segment.Triple1,//success 6

                    //R4TeamA
                    Segment.BullOut,
                    Segment.BullIn,
                    Segment.Single1Out, //success 75

                     //R5TeamA
                    Segment.Single1Out,
                    Segment.Single1Out,
                    Segment.Single1Out, //miss 0

                     //R6TeamA
                    Segment.Single1Out,
                    Segment.Single1Out,
                    Segment.Single1Out, //miss 0

                     //R7TeamA
                    Segment.Single1Out,
                    Segment.Single1Out,
                    Segment.Single1Out, //miss 0

                     //R8TeamA
                    Segment.Single1Out,
                    Segment.Single1Out,
                    Segment.Single1Out, //miss 0

                    },
                    1,
                    "A",
                    new GameRuleOniren.OnirenMode[]{ OnirenMode.None }
                })
                .SetName("1player  round score  TeamA int[]{2,2,6,50,0,0,0,0}")
            .Returns(new int[]{2,2,6,50,0,0,0,0}),

            //testcase その1 1人で8Rまでやった場合 OniSingle設定してもスコアが変わらないことの確認用
            new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                    //R1TeamA
                    Segment.Single1Out,
                    Segment.Single1Out,
                    Segment.Single2In,//faild

                    //R2TeamA
                    Segment.Double1,
                    Segment.Single1Out,
                    Segment.Single1Out,//success 2

                    //R3TeamA
                    Segment.Triple1,
                    Segment.Single1Out,
                    Segment.Triple1,//success 6

                    //R4TeamA
                    Segment.BullOut,
                    Segment.BullIn,
                    Segment.Single1Out, //success 75

                     //R5TeamA
                    Segment.Single1Out,
                    Segment.Single1Out,
                    Segment.Single1Out, //miss 0

                     //R6TeamA
                    Segment.Single1Out,
                    Segment.Single1Out,
                    Segment.Single1Out, //miss 0

                     //R7TeamA
                    Segment.Single1Out,
                    Segment.Single1Out,
                    Segment.Single1Out, //miss 0

                     //R8TeamA
                    Segment.Single1Out,
                    Segment.Single1Out,
                    Segment.Single1Out, //miss 0

                    },
                    1,
                    "A",
                    new OnirenMode[]{ OnirenMode.OniSingle }
                })
                .SetName("1player  round score  TeamA int[]{2,2,6,50,0,0,0,0}")
            .Returns(new int[]{2,2,6,50,0,0,0,0}),
        };



        [TestCaseSource(nameof(RoundScoreTestcases))]
        public int[] RoundScoreTest(Segment[] hits, int playerCount, string unitId, OnirenMode mode)
        {
            var referee = StartMatchProceed(hits, playerCount, mode: mode, testrandamtarget: true);

            List<int> roundlist = new List<int>();
            foreach (var item in referee.AllRoundsAtCurrentUnit)
            {
                roundlist.Add(referee.Scorer.TotalScoreInRound(unitId, item.No - 1));
            }

            return roundlist.ToArray();
        }

    }
}
