using System;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.Oniren;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.oniren
{
    public partial class OnirenRefereeTest
    {
        public static TestCaseData[] OnirenMaxRoundTestdataArray = new TestCaseData[]
        {

            new TestCaseData(new object[]
                {

                    new Segment[]
                    {

                    },
                    2,
                    "A",
                })
                .SetName(
                    "Oniren game is MaxRound 99")
            .Returns(1000),
        };

        [TestCaseSource(nameof(OnirenMaxRoundTestdataArray))]
        public int MaxRoundTest_MaxRoundCountIs99(Segment[] segments, int playerCount, string teamId)
        {
            var referee = StartMatchProceed(segments, playerCount, mode: GameRuleOniren.OnirenMode.None);
            return referee.Match.Rule.MaxRound;
        }
    }
}