using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.countup
{
    public partial class CountUpRefereeTest
    {
        public static TestCaseData[] TeamSeparateBullTarget20RateByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    "TestCaseA",
                    1,
                    1,
                    "TestA_1",
                    BullOption.SeparateBull
                })
                .SetName(
                    "Test Case A（1人プレイ）: 0.417")
                .Returns(0.417f),

            new TestCaseData(new object[]
                {
                    "TestCaseB",
                    1,
                    1,
                    "TestA_1",
                    BullOption.SeparateBull
                })
                .SetName(
                    "Test Case B（1人プレイ）: 0.458")
                .Returns(0.458f),

            new TestCaseData(new object[]
                {
                    "TestCaseC",
                    1,
                    1,
                    "TestA_1",
                    BullOption.SeparateBull
                })
                .SetName(
                    "Test Case C（1人プレイ）: 0.542")
                .Returns(0.542f),

            new TestCaseData(new object[]
                {
                    "TestCaseD",
                    1,
                    2,
                    "TestA_1",
                    BullOption.SeparateBull
                })
                .SetName(
                    "Test Case D A1（ペアプレイ）: 0.500")
                .Returns(0.500f),

            new TestCaseData(new object[]
                {
                    "TestCaseD",
                    1,
                    2,
                    "TestA_2",
                    BullOption.SeparateBull
                })
                .SetName(
                    "Test Case D A2（ペアプレイ）: 0.417")
                .Returns(0.417f),
        };

        [TestCaseSource(nameof(TeamSeparateBullTarget20RateByIndex))]
        public float SeparateBullTest4_Target20Rate(string testCaseType, int unitCount, int memberCount, string granId, BullOption bullOption = BullOption.FatBull)
        {
            var referee = StartSkipMatchProceed(testCaseType, unitCount, memberCount, bullOption);
            return referee.Scorer.Target20Rate(granId);
        }
    }
}