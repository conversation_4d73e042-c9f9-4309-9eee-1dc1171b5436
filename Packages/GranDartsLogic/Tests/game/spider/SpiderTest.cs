using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.spider;
using com.luxza.grandartslogic.interfaces;
using System;
using System.Collections.Generic;
using com.luxza.grandartslogic.domain.game.zeroone;

namespace com.luxza.grandartslogic.tests.game.spider
{
    public partial class SpiderTest
    {
        private string[] testplayernameArray = {
            "A",
            "B",
            "C",
            "D",
            "E",
            "F",
            "G",
            "H"
        };

        private RefereeSpider MakeSpiderRef
        (
            List<Unit> TeamsList,
            BullOption bullOption = BullOption.FatBull,
            OutCondition outCondition = OutCondition.OpenOut,
            SpiderGameLevel level = SpiderGameLevel.Normal,
            int testArrange = 0
        )
        {
            return MatchMaker.MakeSpider
            (
                TeamsList.ToArray(),
                new GameRuleSpider(bullOption: bullOption, level: level, outCondition: outCondition),
                testArrange
            );
        }

        private RefereeSpider StartMatchProceed
        (
            Segment[] hits,
            int playerCount,
            BullOption bullOption = BullOption.FatBull,
            OutCondition outCondition = OutCondition.OpenOut,
            SpiderGameLevel level = SpiderGameLevel.Normal,
            int testArrange = 0
        )
        {
            List<Unit> TeamsList = new List<Unit>();

            for (int i = 0; i < playerCount; i++)
            {
                TeamsList.Add
                (
                    UnitFactory.Instance.TeamUp
                    (
                        testplayernameArray[i],
                        new Player[] {
                            new Player("Test" + testplayernameArray[i], i + 1),
                        },
                        i
                    )
                );
            }
            var referee = MakeSpiderRef
            (
                TeamsList,
                bullOption,
                outCondition,
                level,
                testArrange
            );
            return StartMatchAndToProceed(hits, referee);
        }

        private RefereeSpider StartMatchAndToProceed
        (
            Segment[] hits,
            RefereeSpider referee
        )
        {
            referee.StartMatch();

            bool needToNextRound = false;

            foreach (var segment in hits)
            {
                if (segment == Segment.Change)
                {
                    referee.ChangeToNextTeam();
                    needToNextRound = false;
                    continue;
                }

                if (needToNextRound)
                {
                    referee.ChangeToNextTeam();
                    needToNextRound = false;
                }

                referee.AcceptHit(segment);

                if (referee.IsThrowCountOverMaxThrowInRound)
                {
                    needToNextRound = true;
                }
            }

            return referee;
        }
    }
}