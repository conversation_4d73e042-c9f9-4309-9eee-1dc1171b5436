using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.unit
{
    public partial class ParticipantsTest
    {
        [Test]
        public void TestParticipantsAreCreatedCorrectly_2Teams()
        {
            var unit1 = SinglesUnitA(1);
            var unit2 = SinglesUnitB(1);
            var unit3 = SinglesUnitC(2);
            var unit4 = SinglesUnitD(2);

            var units = new Unit[] {
                unit1,
                unit2,
                unit3,
                unit4
            };

            // Act
            var participants = Participants.CreateWithConsiderTeam(units);

            // Assert
            Assert.AreEqual(4, participants.AllUnits.Length);
            Assert.AreEqual(unit1.Id, participants.Unit(0).Id);
            Assert.AreEqual(unit3.Id, participants.Unit(1).Id);
            Assert.AreEqual(unit2.Id, participants.Unit(2).Id);
            Assert.AreEqual(unit4.Id, participants.Unit(3).Id);
        }

        [Test]
        public void TestParticipantsAreCreatedCorrectly_2Teams_with_not_ordered_by_TeamTag()
        {
            var unit1 = SinglesUnitA(1);
            var unit2 = SinglesUnitB(2);
            var unit3 = SinglesUnitC(2);
            var unit4 = SinglesUnitD(1);

            var units = new Unit[] {
                unit1,
                unit2,
                unit3,
                unit4
            };

            // Act
            var participants = Participants.CreateWithConsiderTeam(units);
            // Assert
            Assert.AreEqual(4, participants.AllUnits.Length);
            Assert.AreEqual(unit1.Id, participants.Unit(0).Id);
            Assert.AreEqual(unit2.Id, participants.Unit(1).Id);
            Assert.AreEqual(unit4.Id, participants.Unit(2).Id);
            Assert.AreEqual(unit3.Id, participants.Unit(3).Id);
        }

        [Test]
        public void TestParticipantsAreCreatedCorrectly_3Teams()
        {
            var unit1 = SinglesUnitA(1);
            var unit2 = SinglesUnitB(1);
            var unit3 = SinglesUnitC(2);
            var unit4 = SinglesUnitD(2);
            var unit5 = SinglesUnitE(3);
            var unit6 = SinglesUnitF(3);

            var units = new Unit[] {
                unit1,
                unit2,
                unit3,
                unit4,
                unit5,
                unit6,
            };

            // Act
            var participants = Participants.CreateWithConsiderTeam(units);
            // Assert
            Assert.AreEqual(6, participants.AllUnits.Length);
            Assert.AreEqual(unit1.Id, participants.Unit(0).Id);
            Assert.AreEqual(unit3.Id, participants.Unit(1).Id);
            Assert.AreEqual(unit5.Id, participants.Unit(2).Id);
            Assert.AreEqual(unit2.Id, participants.Unit(3).Id);
            Assert.AreEqual(unit4.Id, participants.Unit(4).Id);
            Assert.AreEqual(unit6.Id, participants.Unit(5).Id);
        }

        [Test]
        public void TestParticipantsAreCreatedCorrectly_OneUnit()
        {
            var unit1 = SinglesUnitA(1);

            var units = new Unit[] {
                unit1
            };

            // Act
            var participants = Participants.CreateWithConsiderTeam(units);
            // Assert
            Assert.AreEqual(1, participants.AllUnits.Length);
            Assert.AreEqual(unit1.Id, participants.Unit(0).Id);
        }
    }
}