using com.luxza.grandartslogic.domain.game;
using System.Collections.Generic;

namespace com.luxza.grandartslogic
{
    //メドレーのレグごとのゲーム結果
    public class MedleyLegResult
    {
        private Unit Winner;
        private Dictionary<string, int> TeamScoreDic;
        private Dictionary<string, double> Team80PersentDic;
        private Dictionary<string, double> Member80PersentDic;
        public MedleyLegResult(Dictionary<string, int> teamScoreDic,
                               Dictionary<string, double> team80Persents,
                               Dictionary<string, double> member80Persents,
                               Unit winner)
        {
            Winner = winner;
            TeamScoreDic = teamScoreDic;
            Team80PersentDic = team80Persents;

            Member80PersentDic = member80Persents;
            Winner = winner;
        }

        public Unit GetLegWinner() => Winner;

        public bool IsLegWinnerTeam(string teamId) => Winner.Id.Equals(teamId);

        public int GetLegTeamScore(string teamId) => TeamScoreDic[teamId];
        public double GetLegMember80Percent(string granId) => Member80PersentDic[granId];
        public double GetLegTeam80Percent(string teamId) => Team80PersentDic[teamId];



    }

}