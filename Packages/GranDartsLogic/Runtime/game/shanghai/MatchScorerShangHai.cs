using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.extentions;

namespace com.luxza.grandartslogic.domain.game.shanghai
{
    public class MatchScorerShangHai : IMatchScorer
    {
        private readonly List<PlayerShanghaiData> _playerdatas = new List<PlayerShanghaiData>();
        private readonly GameRuleShangHai _ruleShangHai;
        public MatchShangHai Match { get; }
        private NotSupportedException _notSupportedInputTypeException =
            new NotSupportedException("Round must be either PerDartsInput or PerVisitInput");
        
        public MatchScorerShangHai(MatchShangHai match)
        {
            Match = match;
            foreach (var t in match.ParticipantTeams.AllUnits)
            {
                foreach (var m in t.AllMember)
                {
                    _playerdatas.Add(new PlayerShanghaiData(t.Id, m.GranId));
                }
            }
        }
        
        public int CurrentScore(string unitId)
        {
            var unit = Match.ParticipantTeams.Unit(unitId);
            return unit.Progress.CurrentTotalScore(Score);
        }

        public int Score(string teamId, int roundIndex, int throwIndex)
        {
            var roundInput = Match.ParticipantTeams.Unit(teamId).Progress.Rounds[roundIndex].GetRoundComponent<IRoundInput>();
            if (roundInput is VisitInput perVisitInput)
            {
                return perVisitInput.Score;
            }

            if (roundInput is SegmentInput perDartsInput)
            {
                return Score(perDartsInput.Throws[throwIndex].VirtualHitArea);
            }

            throw _notSupportedInputTypeException;
        }

        public int TotalScoreInRound(string teamId, int roundIndex)
        {
            var roundInput = Match.ParticipantTeams.Unit(teamId).Progress.Rounds[roundIndex].GetRoundComponent<IRoundInput>();
            if (roundInput is SegmentInput
                perDartsInput)
            {
                return perDartsInput.Throws.Sum(t => Score(t.VirtualHitArea));
            }

            if (roundInput is VisitInput perVisitInput)
            {
                return perVisitInput.Score;
            }

            throw _notSupportedInputTypeException;
        }
        
        /// <summary>
        /// Roundのスコアを返します。
        /// </summary>
        /// <param name="round"></param>
        /// <returns></returns>
        public int TotalScoreInRound(Round round)
        {
            if (round.TryGetRoundComponent<SegmentInput>(out var segmentInput))
            {
                return segmentInput.Throws.Sum(t => Score(t.VirtualHitArea));
            }
            else if (round.TryGetRoundComponent<VisitInput>(out var visitInput))
            {
                return visitInput.Score;
            }

            throw new InvalidRoundInput("This round has no SegmentInput or VisitInput");
        }

        public int Score(Segment hit)
        {
            if (hit == null)
            {
                return 0;
            }
            if (hit.IsSingle)
            {
                return 1;
            }
            if (hit.IsDouble)
            {
                return 2;
            }
            if (hit.IsTriple)
            {
                return 3;
            }
            return 0;
        }

        public int GetTotalScore(string unitId)
        {
            return _playerdatas
                .Where(p => p.TeamId == unitId)
                .Sum(p => p.TotalScore);
        }

        /// <summary>
        /// 检查指定单位在指定回合是否已达成Shanghai
        /// </summary>
        /// <param name="unitId">单位ID</param>
        /// <param name="roundIndex">回合索引（0-based）</param>
        /// <returns>是否已达成Shanghai</returns>
        public bool IsShanghaiAchieved(string unitId, int roundIndex)
        {
            return _playerdatas
                .Where(p => p.TeamId == unitId)
                .Any(p => p.IsShanghaiAchievedInRound(roundIndex));
        }

        public void RecordHit(string teamId, string granId, int segmentValue, int roundIndex, int currentRoundNo = 0)
        {
            var playerData = _playerdatas.First(p => p.TeamId == teamId && p.GranId == granId);
            playerData.RecordHit(segmentValue, roundIndex, currentRoundNo);
        }

        /// <summary>
        /// 回退指定玩家的最后一镖
        /// </summary>
        /// <param name="teamId">队伍ID</param>
        /// <param name="granId">玩家GranID</param>
        /// <returns>是否成功回退</returns>
        public bool RevertLastThrow(string teamId, string granId)
        {
            var playerData = _playerdatas.First(p => p.TeamId == teamId && p.GranId == granId);
            return playerData.RevertLastThrow();
        }

        /// <summary>
        /// 回退指定玩家的整个轮次
        /// </summary>
        /// <param name="teamId">队伍ID</param>
        /// <param name="granId">玩家GranID</param>
        /// <param name="roundIndex">轮次索引（0-based）</param>
        /// <returns>是否成功回退</returns>
        public bool RevertRound(string teamId, string granId, int roundIndex)
        {
            var playerData = _playerdatas.First(p => p.TeamId == teamId && p.GranId == granId);
            return playerData.RevertRound(roundIndex);
        }

        /// <summary>
        /// 将指定单位的当前分数减半
        /// </summary>
        /// <param name="unitId">单位ID</param>
        /// <param name="roundNo">产生减半惩罚的轮次号</param>
        public void HalfScore(string unitId, int roundNo)
        {
            var playerDatas = _playerdatas.Where(p => p.TeamId == unitId);
            foreach (var playerData in playerDatas)
            {
                playerData.HalfScore(roundNo);
            }
        }

        /// <summary>
        /// 重新开始游戏时清除所有分数数据
        /// </summary>
        public void ReStartGame()
        {
            // 清除所有玩家的分数记录
            foreach (var playerData in _playerdatas)
            {
                playerData.ClearAllScores();
            }
        }
        
        /// <summary>
        /// 检查指定单位的指定回合是否有减半惩罚
        /// </summary>
        /// <param name="unitId">单位ID</param>
        /// <param name="roundNo">回合号</param>
        /// <returns>指定回合是否有减半惩罚</returns>
        public bool HasHalfPenalty(string unitId, int roundNo)
        {
            var playerDatas = _playerdatas.Where(p => p.TeamId == unitId);
            return playerDatas.Any(playerData => playerData.HasHalfPenalty(roundNo));
        }

        /// <summary>
        /// 回退指定单位指定轮次的减半惩罚
        /// </summary>
        /// <param name="unitId">单位ID</param>
        /// <param name="roundNo">轮次号</param>
        /// <returns>是否成功回退</returns>
        public bool RevertHalfPenaltyForRound(string unitId, int roundNo)
        {
            var playerDatas = _playerdatas.Where(p => p.TeamId == unitId);
            bool anyReverted = false;
            foreach (var playerData in playerDatas)
            {
                if (playerData.RevertHalfPenaltyForRound(roundNo))
                {
                    anyReverted = true;
                }
            }
            return anyReverted;
        }
        
        /// <summary>
        /// 获取指定单位的总分数
        /// </summary>
        /// <param name="unitId">单位ID</param>
        /// <returns>总分数</returns>
        public int TotalScore(string unitId)
        {
            return _playerdatas
                .Where(p => p.TeamId == unitId)
                .Sum(p => p.TotalScore);
        }

        private class PlayerShanghaiData
        {
            public string TeamId { get; }
            public string GranId { get; }
            private readonly List<int> _scores = new List<int>();
            private readonly Dictionary<int, List<int>> _roundHits = new Dictionary<int, List<int>>();
            private readonly List<bool> _isHalfPenalty = new List<bool>(); // 标记哪些分数是减半惩罚
            private readonly List<int> _halfPenaltyRounds = new List<int>(); // 记录减半惩罚对应的轮次

            public PlayerShanghaiData(string teamId, string granId)
            {
                TeamId = teamId;
                GranId = granId;
            }

            public void RecordHit(int segmentValue, int roundIndex, int currentRoundNo)
            {
                if (!_roundHits.ContainsKey(roundIndex))
                {
                    _roundHits[roundIndex] = new List<int>();
                }

                _roundHits[roundIndex].Add(segmentValue);

                // Calculate score for this throw
                int score = 0;
                if (segmentValue == 1) score = 1;
                else if (segmentValue == 2) score = 2;
                else if (segmentValue == 3) score = 3;

                _scores.Add(score);
                _isHalfPenalty.Add(false); // 正常得分，不是减半惩罚
                _halfPenaltyRounds.Add(-1); // -1表示不是减半惩罚

                // 检查当前回合是否达成Shanghai，如果是则重新计算该回合的所有得分
                if (IsShanghaiAchievedInRound(roundIndex))
                {
                    RecalculateRoundScoreForShanghai(roundIndex, currentRoundNo);
                }
            }

            public int TotalScore => _scores.Sum();

            /// <summary>
            /// 回退最后一镖
            /// </summary>
            /// <returns>是否成功回退</returns>
            public bool RevertLastThrow()
            {
                if (_scores.Count == 0) return false;

                // 检查最后一个分数是否是减半惩罚
                bool isLastHalfPenalty = _isHalfPenalty[_scores.Count - 1];

                if (isLastHalfPenalty)
                {
                    // 如果最后一个是减半惩罚，移除它
                    _scores.RemoveAt(_scores.Count - 1);
                    _isHalfPenalty.RemoveAt(_isHalfPenalty.Count - 1);
                    _halfPenaltyRounds.RemoveAt(_halfPenaltyRounds.Count - 1);

                    // 继续回退实际的投掷
                    if (_scores.Count > 0)
                    {
                        return RevertLastThrow(); // 递归调用回退实际投掷
                    }
                    return true;
                }

                // 从最后一个轮次中移除最后一镖
                var lastRoundWithHits = _roundHits.Where(kvp => kvp.Value.Count > 0)
                                                  .OrderByDescending(kvp => kvp.Key)
                                                  .FirstOrDefault();

                if (lastRoundWithHits.Value != null && lastRoundWithHits.Value.Count > 0)
                {
                    int lastRoundIndex = lastRoundWithHits.Key;
                    int lastRoundNo = lastRoundIndex + 1; // roundIndex是0-based，roundNo是1-based

                    // 检查这是否是该轮次的最后一镖
                    bool isLastThrowInRound = lastRoundWithHits.Value.Count == 1;

                    // 移除投掷记录
                    lastRoundWithHits.Value.RemoveAt(lastRoundWithHits.Value.Count - 1);

                    // 移除分数记录
                    _scores.RemoveAt(_scores.Count - 1);
                    _isHalfPenalty.RemoveAt(_isHalfPenalty.Count - 1);
                    _halfPenaltyRounds.RemoveAt(_halfPenaltyRounds.Count - 1);

                    // 如果轮次没有命中记录了，移除该轮次
                    if (lastRoundWithHits.Value.Count == 0)
                    {
                        _roundHits.Remove(lastRoundWithHits.Key);
                    }
                    else
                    {
                        // 如果该轮次还有其他投掷，需要重新计算该轮次的得分
                        RecalculateRoundScoreAfterRevert(lastRoundIndex, lastRoundNo);
                    }

                    // 如果这是该轮次的最后一镖，检查并移除该轮次的减半惩罚
                    if (isLastThrowInRound)
                    {
                        RemoveHalfPenaltyForRound(lastRoundNo);
                    }

                    return true;
                }

                return false;
            }

            /// <summary>
            /// 将当前分数减半
            /// </summary>
            /// <param name="roundNo">产生减半惩罚的轮次号</param>
            public void HalfScore(int roundNo)
            {
                int currentTotal = TotalScore;
                int halfScore = (int)Math.Ceiling(currentTotal / 2.0f);
                int penalty = currentTotal - halfScore;

                // 添加一个负分数来实现减半效果，保持历史记录完整
                if (penalty > 0)
                {
                    _scores.Add(-penalty);
                    _isHalfPenalty.Add(true); // 标记为减半惩罚
                    _halfPenaltyRounds.Add(roundNo); // 记录产生减半惩罚的轮次
                }
            }

            /// <summary>
            /// 回退指定轮次的减半惩罚
            /// </summary>
            /// <param name="roundNo">轮次号</param>
            /// <returns>是否成功回退</returns>
            public bool RevertHalfPenaltyForRound(int roundNo)
            {
                // 从后往前查找指定轮次的减半惩罚
                for (int i = _scores.Count - 1; i >= 0; i--)
                {
                    if (_isHalfPenalty[i] && _halfPenaltyRounds[i] == roundNo)
                    {
                        _scores.RemoveAt(i);
                        _isHalfPenalty.RemoveAt(i);
                        _halfPenaltyRounds.RemoveAt(i);
                        return true;
                    }
                }
                return false;
            }

            /// <summary>
            /// 检查指定回合是否有减半惩罚
            /// </summary>
            /// <param name="roundNo">回合号</param>
            /// <returns>指定回合是否有减半惩罚</returns>
            public bool HasHalfPenalty(int roundNo)
            {
                for (int i = 0; i < _isHalfPenalty.Count; i++)
                {
                    if (_isHalfPenalty[i] && _halfPenaltyRounds[i] == roundNo)
                    {
                        return true;
                    }
                }
                return false;
            }

            /// <summary>
            /// 移除指定轮次的减半惩罚（内部方法）
            /// </summary>
            /// <param name="roundNo">轮次号</param>
            private void RemoveHalfPenaltyForRound(int roundNo)
            {
                for (int i = _scores.Count - 1; i >= 0; i--)
                {
                    if (_isHalfPenalty[i] && _halfPenaltyRounds[i] == roundNo)
                    {
                        _scores.RemoveAt(i);
                        _isHalfPenalty.RemoveAt(i);
                        _halfPenaltyRounds.RemoveAt(i);
                        break; // 只移除一个减半惩罚
                    }
                }
            }

            /// <summary>
            /// 清除所有分数记录（重新开始游戏时使用）
            /// </summary>
            public void ClearAllScores()
            {
                _scores.Clear();
                _roundHits.Clear();
                _isHalfPenalty.Clear();
                _halfPenaltyRounds.Clear();
            }
            
            /// <summary>
            /// 回退整个轮次
            /// </summary>
            /// <param name="roundIndex">轮次索引（0-based）</param>
            /// <returns>是否成功回退</returns>
            public bool RevertRound(int roundIndex)
            {
                if (!_roundHits.ContainsKey(roundIndex))
                {
                    return false;
                }

                var roundHits = _roundHits[roundIndex];
                int roundNo = roundIndex + 1; // roundIndex是0-based，roundNo是1-based

                // 检查该回合是否达成Shanghai
                bool isShanghai = roundHits.Contains(1) && roundHits.Contains(2) && roundHits.Contains(3);

                // 从_scores中移除该轮次的所有分数
                int hitCount = roundHits.Count;
                for (int i = 0; i < hitCount; i++)
                {
                    if (_scores.Count > 0)
                    {
                        _scores.RemoveAt(_scores.Count - 1);
                        _isHalfPenalty.RemoveAt(_isHalfPenalty.Count - 1);
                        _halfPenaltyRounds.RemoveAt(_halfPenaltyRounds.Count - 1);
                    }
                }

                // 移除整个轮次
                _roundHits.Remove(roundIndex);

                // 移除该轮次的减半惩罚（如果有的话）
                RemoveHalfPenaltyForRound(roundNo);

                return true;
            }

            /// <summary>
            /// 重新计算达成Shanghai的回合得分
            /// </summary>
            /// <param name="roundIndex">回合索引</param>
            /// <param name="currentRoundNo">当前回合号</param>
            private void RecalculateRoundScoreForShanghai(int roundIndex, int currentRoundNo)
            {
                if (!_roundHits.ContainsKey(roundIndex))
                {
                    return;
                }

                var roundHits = _roundHits[roundIndex];
                int shanghaiScore = currentRoundNo * 10;

                // 找到该回合对应的分数索引范围
                int startIndex = -1;
                int endIndex = _scores.Count - 1;

                // 从后往前找到该回合的第一个分数
                int hitCount = 0;
                for (int i = _scores.Count - 1; i >= 0; i--)
                {
                    if (hitCount < roundHits.Count)
                    {
                        if (startIndex == -1)
                        {
                            startIndex = i - (roundHits.Count - 1);
                        }
                        hitCount++;
                    }
                    else
                    {
                        break;
                    }
                }

                // 将该回合的所有得分替换为Shanghai得分：第一镖得Shanghai分数，其他镖得0分
                if (startIndex >= 0)
                {
                    for (int i = startIndex; i <= endIndex; i++)
                    {
                        if (i == startIndex)
                        {
                            // 第一镖得Shanghai分数
                            _scores[i] = shanghaiScore;
                        }
                        else
                        {
                            // 其他镖得0分
                            _scores[i] = 0;
                        }
                    }
                }
            }

            /// <summary>
            /// 回退后重新计算回合得分
            /// </summary>
            /// <param name="roundIndex">回合索引</param>
            /// <param name="currentRoundNo">当前回合号</param>
            private void RecalculateRoundScoreAfterRevert(int roundIndex, int currentRoundNo)
            {
                if (!_roundHits.ContainsKey(roundIndex))
                {
                    return;
                }

                var roundHits = _roundHits[roundIndex];

                // 检查回退后该回合是否仍然达成Shanghai
                bool stillShanghai = roundHits.Contains(1) && roundHits.Contains(2) && roundHits.Contains(3);

                // 找到该回合对应的分数索引范围
                int startIndex = -1;
                int endIndex = _scores.Count - 1;

                // 从后往前找到该回合的第一个分数
                int hitCount = 0;
                for (int i = _scores.Count - 1; i >= 0; i--)
                {
                    if (hitCount < roundHits.Count)
                    {
                        if (startIndex == -1)
                        {
                            startIndex = i - (roundHits.Count - 1);
                        }
                        hitCount++;
                    }
                    else
                    {
                        break;
                    }
                }

                // 重新计算该回合的得分
                if (startIndex >= 0)
                {
                    for (int i = 0; i < roundHits.Count; i++)
                    {
                        int scoreIndex = startIndex + i;
                        if (scoreIndex < _scores.Count)
                        {
                            if (stillShanghai)
                            {
                                // 仍然是Shanghai，第一镖得Shanghai分数，其他镖得0分
                                if (i == 0)
                                {
                                    _scores[scoreIndex] = currentRoundNo * 10;
                                }
                                else
                                {
                                    _scores[scoreIndex] = 0;
                                }
                            }
                            else
                            {
                                // 不再是Shanghai，恢复正常得分
                                int segmentValue = roundHits[i];
                                int normalScore = 0;
                                if (segmentValue == 1) normalScore = 1;
                                else if (segmentValue == 2) normalScore = 2;
                                else if (segmentValue == 3) normalScore = 3;
                                _scores[scoreIndex] = normalScore;
                            }
                        }
                    }
                }
            }

            /// <summary>
            /// 检查指定回合是否达成Shanghai
            /// </summary>
            /// <param name="roundIndex">回合索引</param>
            /// <returns>是否达成Shanghai</returns>
            public bool IsShanghaiAchievedInRound(int roundIndex)
            {
                if (!_roundHits.ContainsKey(roundIndex - 1))
                {
                    return false;
                }

                var roundHits = _roundHits[roundIndex - 1];
                // Shanghai规则：在同一轮中命中该轮对应数字的Single(1)、Double(2)和Triple(3)
                return roundHits.Contains(1) && roundHits.Contains(2) && roundHits.Contains(3);
            }
        }
    }
}
